Техническое задание 

Разработка корпоративного  сайта Setmee согласно стиля лендинга https://setmee-app.windsurf.build/  на Next.js + TypeScript + Tailwind CSS
https://setmee-app.windsurf.build/contact
1. Общие положения

Создать корпоративный сайт по структуре и функционалу, описанным ниже, с использованием Next.js (TypeScript), Tailwind CSS (utility-first), без сторонних UI-библиотек.
Код должен быть чистым, масштабируемым, легко поддерживаемым, с выносом статических данных в отдельные файлы.

2. Технологический стек
 • Фреймворк: Next.js (актуальная LTS)
 • Язык: TypeScript (весь проект)
 • Стилизация: Tailwind CSS (utility-first, без UI-библиотек)
 • Контент: Статические данные (списки, преимущества, тарифы, FAQ) — в отдельных JSON/TS-файлах
 • Изображения: В папке ⁠/public⁠, использовать Next.js Image
 • SEO: Уникальные title, description, OG-теги через Head
 • Формы: Валидация, интеграция с Formspree/аналогом, статус отправки
 • Анимации: Микроанимации (fade-in, hover, покачивание CTA через 15 сек)
 • Персонализация CTA: Через cookie (см. ниже)
 • Квиз: Реализовать квиз-форму (см. ниже)
 • Адаптивность: Desktop, tablet, mobile
 • Кроссбраузерность: Chrome, Firefox, Safari, Edge

3. Структура проекта/pages
  index.tsx                — Главная
  [service].tsx            — Каждая услуга (внедрение, оптимизация, интеграции)
  solutions/index.tsx      — Хаб отраслей
  solutions/[vertical].tsx — Отраслевые решения
  about.tsx                — О компании
  blog/index.tsx           — Блог
  contact.tsx              — Контакты
  _app.tsx, _document.tsx  — Базовые файлы

/components
  Header.tsx, Footer.tsx
  Секции для каждой страницы (см. ниже)
  Button.tsx, Card.tsx, ContactForm.tsx, QuizForm.tsx, TrustMarkers.tsx, AnimatedCTA.tsx, и др.

/public
  Все изображения, иконки, favicon

/styles
  globals.css — Tailwind base + кастомные стили

/data
  Статические данные для секций (услуги, тарифы, FAQ, отрасли, преимущества и т.д.)

4. Страницы и их структура

4.1 Главная (⁠index.tsx⁠)
 • HeroSection: Заголовок, подзаголовок, сертифицированный партнёр, персонализированный CTA (по cookie), лид-магнит
 • Преимущества в цифрах (опыт, проекты, менеджеры)
 • Анонсы услуг (каждая — отдельная страница, кнопка “Узнать больше”)
 • Решения по отраслям (иконки, переходы на отраслевые страницы)
 • Финальный CTA: Форма (Имя, Email, Телефон), покачивание кнопки через 15 сек
 • Trust-маркеры (блок “Нам доверяют”)
 • Микроанимации (fade-in, hover, покачивание CTA)

4.2 Услуги (отдельные страницы)
 • ⁠/implementation⁠ — Внедрение Kommo CRM
 • ⁠/optimization⁠ — Оптимизация Kommo CRM
 • ⁠/integrations⁠ — Интеграции Kommo через Make

Каждая страница услуги:
 • Вступление (заголовок, описание)
 • Для кого услуга (проблемы)
 • Таймлайн (шаги проекта)
 • Trust-маркеры (блок “Нам доверяют”)
 • Промежуточный CTA (середина страницы)
 • Результаты (что получит клиент)
 • Тарифы (если применимо)
 • Сравнительная таблица/карточки “до/после”
 • FAQ (аккордеон)
 • Финальный CTA: Форма (Имя, Телефон, Email, Комментарий, чекбокс “получить подарок”), статус отправки, автоматизация отправки подарка (описание логики)
 • Микроанимации (fade-in, hover, покачивание CTA)

4.3 Решения по отраслям
 • ⁠/solutions/index.tsx⁠ — Хаб отраслей (иконки, переходы)
 • ⁠/solutions/[vertical].tsx⁠ — Страница отрасли (пример: /solutions/real-estate)
 ▫ Заголовок, подзаголовок
 ▫ Описание проблем отрасли
 ▫ Как решает Kommo (блок “что даёт”)
 ▫ Индивидуальная воронка (схема/описание)
 ▫ Сценарии автоматизации (Make/без него)
 ▫ Интерактив (калькулятор, PDF)
 ▫ Кейс/результат внедрения
 ▫ Финальный CTA + форма

4.4 О компании (⁠about.tsx⁠)
 • Вступление (заголовок, описание)
 • Кто мы такие (команда, опыт, сертификация)
 • Ключевые компетенции
 • Преимущества работы с нами
 • Сертификаты и достижения
 • Миссия и ценности
 • Финальный CTA (связаться с нами)

4.5 Блог 
реализовать только пункт меню 

4.6 Контакты (⁠contact.tsx⁠)
 • Заголовок, описание
 • Интерактивные способы связи (email, Telegram, WhatsApp с иконками)
 • Форма обратной связи (Имя, Email*, Телефон, Тема, Комментарий, чекбокс согласия)
 • Trust-маркеры (сертификат, внедрения, сценарии)
 • Микроанимации

5. Функциональные требования
 • SPA-переходы между страницами (Header/Footer)
 • Адаптивность (desktop, tablet, mobile)
 • SEO: уникальные title, description, OG-теги через Head
 • Формы: обязательная валидация, интеграция с email, статус отправки, чекбоксы для лид-магнитов
 • Оптимизация изображений (Next.js Image, ленивая загрузка)
 • SSR/SSG (getStaticProps/getServerSideProps) по необходимости
 • Кроссбраузерность (актуальные браузеры)
 • Микроанимации (fade-in, hover, покачивание CTA)
 • Персонализация CTA (cookie: для повторных визитов — другой текст)
 • Trust-маркеры на всех страницах

6. Критерии приёмки
 • Визуальное и функциональное соответствие структуре и макетам
 • Корректная работа всех форм, CTA, навигации, анимаций
 • Адаптивность и кроссбраузерность
 • Чистый, структурированный TypeScript-код с комментариями
 • README с инструкцией по установке, запуску, сборке, деплою

7. Качество и автоматизация
 • ESLint & Prettier: единые конфиги, npm-скрипты lint/format, ошибки блокируют сборку
 • Husky (опц.): pre-commit npm run lint && npm run format
 • CI/CD: GitHub Actions (install → lint → build → lighthouse → deploy), деплой на Vercel/Netlify, preview для PR
 • Lighthouse: Performance ≥ 90, LCP ≤ 2.5c, CLS ≤ 0.1
 • Доступность (a11y): alt, семантические теги, axe-linter в CI
 • SEO: sitemap.xml, robots.txt, OG/Twitter Cards, next-seo или вручную

8. Прочее
 • Все секции и повторяющиеся элементы — отдельные компоненты с пропсами
 • Статические данные — в отдельные файлы
 • Не использовать сторонние UI-библиотеки (кроме Tailwind)
 • Не допускать дублирования кода и стилей
 • Все CTA ведут на релевантные формы/страницы
 • Квиз — отдельный компонент, вызывать на главной и как pop-up при уходе

9. Блог (опционально)
