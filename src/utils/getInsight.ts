import { RoiResult } from './calculateRoi';

interface AdditionalData {
  niche?: string;
  managers?: number;
  channels?: string[];
}

export const getInsight = (result: RoiResult, additionalData: AdditionalData = {}): string => {
  const { leads, conversion, timePerLead } = result.initialData;
  const { deltaRevenue, savedHours } = result;
  const { niche, managers, channels } = additionalData;

  // Helper function for consistent number formatting
  const formatCurrency = (amount: number) => `$${new Intl.NumberFormat('en-US').format(amount)}`;

  // Threshold Hint for low revenue potential
  if (deltaRevenue < 200 && deltaRevenue > 0) {
    return `📈 The potential revenue increase with current data is modest. For your business scale, automation would be most effective with deep customization to tackle specific bottlenecks. Let's discuss them.`;
  }

  // Standard Diagnostics & Personalization
  if (leads > 200 && conversion < 10) {
    let insight = `💡 You have a high volume of leads but a low conversion rate (${conversion}%). This is a classic case of manager overload. Automation can help you process them faster and avoid losing clients. The potential revenue increase is at least ${formatCurrency(deltaRevenue)}.`;

    if (niche === 'Online Education' || niche === 'SaaS') {
      insight += ` This is especially relevant for ${niche}, where speed of lead response is crucial.`;
    }
    return insight;
  }

  if (conversion > 30 && savedHours && savedHours > 40) {
    let insight = `💡 Your conversion rate is already solid (${conversion}%). Your main growth opportunity is time savings. The ${savedHours} hours saved per month can be invested in product development or scaling, rather than routine tasks.`;

    if (managers && managers > 5) {
      insight += ` With ${managers} managers, these hours are multiplied across the team, freeing up significant resources.`;
    }
    return insight;
  }

  if (timePerLead && timePerLead > 60) {
    return `💡 You're spending a lot of time (${timePerLead} min) processing each lead. Automating routine tasks can significantly reduce these costs and increase sales department throughput, bringing in an additional ${formatCurrency(deltaRevenue)} per month.`;
  }

  // Channel-specific insights
  if (channels && channels.length > 0) {
    const channelInsight = getChannelSpecificInsight(channels, deltaRevenue);
    if (channelInsight) {
      return channelInsight;
    }
  }

  if (niche === 'Real Estate') {
    return `💡 In Real Estate, timing is everything. Automation can help you be the first to respond to leads, significantly improving your conversion rate and bringing an additional ${formatCurrency(deltaRevenue)} per month.`;
  }

  if (niche === 'E-commerce') {
    return `💡 E-commerce businesses benefit greatly from automated follow-ups and abandoned cart recovery. This can bring an additional ${formatCurrency(deltaRevenue)} per month to your revenue.`;
  }

  return `💡 Even with your current metrics, automation can generate an additional ${formatCurrency(deltaRevenue)} per month by optimizing your sales process.`;
};

// Channel-specific insights based on automation potential
function getChannelSpecificInsight(channels: string[], deltaRevenue: number): string | null {
  const formatCurrency = (amount: number) => `$${new Intl.NumberFormat('en-US').format(amount)}`;

  const channelInsights: Record<string, string> = {
    'instagram': `💡 Instagram automation is a game-changer! Auto-responding to DMs and comments can boost your conversion rate significantly. Expected revenue increase: ${formatCurrency(deltaRevenue)}/month.`,

    'google-ads': `💡 Google Ads + automation = perfect match! Faster lead response improves Quality Score and reduces cost-per-click. Revenue boost: ${formatCurrency(deltaRevenue)}/month.`,

    'email': `💡 Email automation has the highest ROI! Drip campaigns, abandoned cart recovery, and lead nurturing can dramatically increase conversions. Potential: ${formatCurrency(deltaRevenue)}/month.`,

    'facebook': `💡 Facebook lead ads with instant automation create a powerful funnel. Auto-qualification and immediate follow-up maximize conversion rates. Expected gain: ${formatCurrency(deltaRevenue)}/month.`,

    'linkedin': `💡 LinkedIn automation for B2B is highly effective. Automated connection requests and follow-ups can significantly improve your professional network ROI: ${formatCurrency(deltaRevenue)}/month.`,

    'website-form': `💡 Website form automation is essential! Instant notifications, auto-responses, and lead scoring ensure no opportunity is missed. Revenue potential: ${formatCurrency(deltaRevenue)}/month.`,

    'phone-calls': `💡 While calls are personal, automation can help with scheduling, follow-ups, and CRM updates. Even modest automation brings ${formatCurrency(deltaRevenue)}/month in efficiency gains.`,

    'referrals': `💡 Referral automation can systematize your word-of-mouth marketing. Automated thank-you messages and referral tracking increase program effectiveness: ${formatCurrency(deltaRevenue)}/month.`,

    'events': `💡 Event lead automation is crucial! Automated follow-ups within 24 hours of events can increase conversion by 300%. Expected revenue boost: ${formatCurrency(deltaRevenue)}/month.`,

    'content-marketing': `💡 Content marketing automation helps nurture leads through the funnel. Automated content delivery based on interests increases engagement: ${formatCurrency(deltaRevenue)}/month.`,

    'seo': `💡 SEO leads are high-intent! Automated lead capture and immediate follow-up for organic traffic can significantly improve conversion rates: ${formatCurrency(deltaRevenue)}/month.`,

    'other': `💡 Custom automation solutions for your unique channels can unlock hidden potential. Every touchpoint optimized brings revenue: ${formatCurrency(deltaRevenue)}/month.`
  };

  // Find the highest-impact channel
  const priorityChannels = ['email', 'google-ads', 'instagram', 'facebook', 'events', 'linkedin'];

  for (const priority of priorityChannels) {
    if (channels.includes(priority)) {
      return channelInsights[priority];
    }
  }

  // If no priority channels, return insight for the first selected channel
  if (channels.length > 0) {
    const firstChannel = channels[0];
    return channelInsights[firstChannel] || channelInsights['other'];
  }

  return null;
}
