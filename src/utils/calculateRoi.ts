export interface RoiInput {
  leads: number;
  conversion: number;
  avgDealSize: number;
  timePerLead?: number;
  niche?: string;
  managers?: number;
  channels?: string[];
}

export interface RoiResult {
  revenueBefore: number;
  revenueAfter: number;
  deltaRevenue: number;
  gainPercent: number;
  savedHours: number | null;
  dealsBefore: number;
  dealsAfter: number;
  conversionGrowth: number;
  initialData: RoiInput;
  channelAnalysis: {
    selectedChannels: string[];
    conversionBonus: number;
    timeSavingsBonus: number;
    automationPotential: string;
  };
}

interface ChannelMultipliers {
  conversionBonus: number;
  timeSavingsBonus: number;
  automationPotential: string;
}

export const calculateRoi = ({ leads, conversion, avgDealSize, timePerLead, channels }: RoiInput): RoiResult => {
  // Base conversion growth factor (based on industry average)
  let CONVERSION_GROWTH_FACTOR = 1.25; // 25% improvement from basic automation

  // Channel-specific multipliers (conservative estimates - replace with real data)
  const channelMultipliers = getChannelMultipliers(channels || []);
  CONVERSION_GROWTH_FACTOR *= channelMultipliers.conversionBonus;
  
  const baseConversion = conversion;
  const improvedConversion = Math.min(baseConversion * CONVERSION_GROWTH_FACTOR, 100);
  
  const dealsBefore = (leads * baseConversion) / 100;
  const dealsAfter = (leads * improvedConversion) / 100;
  
  const revenueBefore = dealsBefore * avgDealSize;
  const revenueAfter = dealsAfter * avgDealSize;
  const deltaRevenue = revenueAfter - revenueBefore;
  
  const gainPercent = revenueBefore > 0 ? Math.round((deltaRevenue / revenueBefore) * 100) : 0;

  // Calculate time savings with channel multiplier
  let savedHours = timePerLead ? Math.round((leads * timePerLead) / 60) : null;
  if (savedHours && channelMultipliers.timeSavingsBonus > 1) {
    savedHours = Math.round(savedHours * channelMultipliers.timeSavingsBonus);
  }

  return {
    revenueBefore,
    revenueAfter,
    deltaRevenue,
    gainPercent,
    savedHours,
    dealsBefore: Math.round(dealsBefore),
    dealsAfter: Math.round(dealsAfter),
    conversionGrowth: Math.round((CONVERSION_GROWTH_FACTOR - 1) * 100),
    // Raw data for AI diagnostics
    initialData: { leads, conversion, avgDealSize, timePerLead, channels },
    // Channel analysis
    channelAnalysis: {
      selectedChannels: channels || [],
      conversionBonus: channelMultipliers.conversionBonus,
      timeSavingsBonus: channelMultipliers.timeSavingsBonus,
      automationPotential: channelMultipliers.automationPotential,
    },
  };
};

// Channel-specific automation potential and multipliers
// TODO: Replace with real data from your projects and case studies
const CHANNEL_DATA = {
  'website-form': { conversionBonus: 1.0, timeSavingsBonus: 1.1, automationPotential: 'Medium' },
  'instagram': { conversionBonus: 1.05, timeSavingsBonus: 1.2, automationPotential: 'High' },
  'facebook': { conversionBonus: 1.05, timeSavingsBonus: 1.15, automationPotential: 'High' },
  'linkedin': { conversionBonus: 1.03, timeSavingsBonus: 1.1, automationPotential: 'Medium' },
  'google-ads': { conversionBonus: 1.08, timeSavingsBonus: 1.2, automationPotential: 'High' },
  'email': { conversionBonus: 1.1, timeSavingsBonus: 1.25, automationPotential: 'High' },
  'phone-calls': { conversionBonus: 1.0, timeSavingsBonus: 1.05, automationPotential: 'Low' },
  'referrals': { conversionBonus: 1.02, timeSavingsBonus: 1.1, automationPotential: 'Medium' },
  'events': { conversionBonus: 1.05, timeSavingsBonus: 1.15, automationPotential: 'Medium' },
  'content-marketing': { conversionBonus: 1.03, timeSavingsBonus: 1.1, automationPotential: 'Medium' },
  'seo': { conversionBonus: 1.02, timeSavingsBonus: 1.1, automationPotential: 'Medium' },
  'other': { conversionBonus: 1.0, timeSavingsBonus: 1.05, automationPotential: 'Low' },
};

function getChannelMultipliers(channels: string[]): ChannelMultipliers {
  if (!channels || channels.length === 0) {
    return {
      conversionBonus: 1.0,
      timeSavingsBonus: 1.0,
      automationPotential: 'Medium'
    };
  }

  // Calculate weighted average based on selected channels
  let totalConversionBonus = 0;
  let totalTimeSavingsBonus = 0;
  let highestPotential = 'Low';

  const potentialLevels = ['Low', 'Medium', 'High', 'Very High', 'Excellent'];
  let maxPotentialIndex = 0;

  channels.forEach(channel => {
    const channelData = CHANNEL_DATA[channel as keyof typeof CHANNEL_DATA];
    if (channelData) {
      totalConversionBonus += channelData.conversionBonus;
      totalTimeSavingsBonus += channelData.timeSavingsBonus;

      const potentialIndex = potentialLevels.indexOf(channelData.automationPotential);
      if (potentialIndex > maxPotentialIndex) {
        maxPotentialIndex = potentialIndex;
        highestPotential = channelData.automationPotential;
      }
    }
  });

  // Average the bonuses, but cap them to conservative limits
  const avgConversionBonus = Math.min(totalConversionBonus / channels.length, 1.15); // Max 15% bonus
  const avgTimeSavingsBonus = Math.min(totalTimeSavingsBonus / channels.length, 1.3); // Max 30% bonus

  return {
    conversionBonus: avgConversionBonus,
    timeSavingsBonus: avgTimeSavingsBonus,
    automationPotential: highestPotential
  };
}
