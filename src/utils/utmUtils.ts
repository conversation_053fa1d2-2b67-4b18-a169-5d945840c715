'use client';

export interface UTMData {
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
}

const UTM_STORAGE_KEY = 'roi_calculator_utm';

export const saveUTMToSession = (): UTMData => {
  if (typeof window === 'undefined') return {};
  
  const urlParams = new URLSearchParams(window.location.search);
  const utmData: UTMData = {};
  
  // Extract UTM parameters
  const utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];
  
  utmParams.forEach(param => {
    const value = urlParams.get(param);
    if (value) {
      utmData[param as keyof UTMData] = value;
    }
  });
  
  // Save to sessionStorage if we have any UTM data
  if (Object.keys(utmData).length > 0) {
    try {
      sessionStorage.setItem(UTM_STORAGE_KEY, JSON.stringify(utmData));
    } catch (error) {
      console.warn('Failed to save UTM data to sessionStorage:', error);
    }
  }
  
  return utmData;
};

export const getUTMFromSession = (): UTMData => {
  if (typeof window === 'undefined') return {};
  
  try {
    const stored = sessionStorage.getItem(UTM_STORAGE_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch (error) {
    console.warn('Failed to retrieve UTM data from sessionStorage:', error);
    return {};
  }
};

export const clearUTMFromSession = (): void => {
  if (typeof window === 'undefined') return;
  
  try {
    sessionStorage.removeItem(UTM_STORAGE_KEY);
  } catch (error) {
    console.warn('Failed to clear UTM data from sessionStorage:', error);
  }
};

// Hook for using UTM data in components
export const useUTMData = () => {
  const saveUTM = () => saveUTMToSession();
  const getUTM = () => getUTMFromSession();
  const clearUTM = () => clearUTMFromSession();
  
  return { saveUTM, getUTM, clearUTM };
};
