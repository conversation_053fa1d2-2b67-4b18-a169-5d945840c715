import React from 'react';
import LinkButton from '@/components/LinkButton';
import Card from '@/components/Card';
import HomeContactForm from '@/components/HomeContactForm';
import FadeInSection from '@/components/FadeInSection';

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="max-w-4xl">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Smart solutions for your business<br />
              based on Kommo CRM
            </h1>
            <p className="text-lg md:text-xl text-gray-600 mb-8 max-w-2xl">
              We can help you with Kommo, be it implementing a new CRM, installing additional modules, Kommo training or integrating Kommo with other systems.
            </p>
            <div className="mb-12">
              <LinkButton href="/contact" variant="primary" size="lg" className="px-8 py-4 text-lg">
                Talk to an expert
              </LinkButton>
            </div>

            {/* Partner Badge and Stats */}
            <div className="space-y-8">
              <div className="flex items-center">
                <img
                  src="/Dark transparent SVG (1).svg"
                  alt="Kommo Partner"
                  className="h-[57.6px] mr-4"
                />
                <span className="text-gray-600 font-medium">Certified Kommo Partner</span>
              </div>

              <div className="flex flex-wrap gap-8 text-sm">
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
                  <span className="text-gray-900">10+ Years Experience</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
                  <span className="text-gray-900">320+ Projects</span>
                </div>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-600 rounded-full mr-2"></div>
                  <span className="text-gray-900">4000+ Managers Trained</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Services Section */}
      <section id="our-services" className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our services
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              We are here to help
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FadeInSection delay={100}>
              <Card variant="elevated" hover className="text-center p-8 group bg-white">
                <div className="w-16 h-16 mx-auto mb-6 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Kommo implementation</h3>
                <p className="text-gray-600 mb-6">Complete CRM implementation tailored to your business needs</p>
                <LinkButton href="/implementation" variant="outline" size="sm" className="group-hover:bg-blue-600 group-hover:text-white transition-colors">
                  Learn More
                </LinkButton>
              </Card>
            </FadeInSection>

            <FadeInSection delay={200}>
              <Card variant="elevated" hover className="text-center p-8 group bg-white">
                <div className="w-16 h-16 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Optimisation of use</h3>
                <p className="text-gray-600 mb-6">Maximize efficiency and get the most out of your Kommo system</p>
                <LinkButton href="/optimization" variant="outline" size="sm" className="group-hover:bg-green-600 group-hover:text-white transition-colors">
                  Learn More
                </LinkButton>
              </Card>
            </FadeInSection>

            <FadeInSection delay={300}>
              <Card variant="elevated" hover className="text-center p-8 group bg-white">
                <div className="w-16 h-16 mx-auto mb-6 bg-purple-100 rounded-full flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">Customised integrations</h3>
                <p className="text-gray-600 mb-6">Custom solutions that make Kommo even more efficient</p>
                <LinkButton href="/integrations" variant="outline" size="sm" className="group-hover:bg-purple-600 group-hover:text-white transition-colors">
                  Learn More
                </LinkButton>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>



      {/* Industries Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Ready Solutions for Your Industry
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We understand your business specifics. Choose your industry to see automation examples and typical solutions.
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            <FadeInSection delay={100}>
              <div className="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow cursor-pointer border border-gray-100">
                <div className="text-3xl mb-3">🤝</div>
                <div className="text-sm font-medium text-gray-700">B2B Sales</div>
              </div>
            </FadeInSection>
            <FadeInSection delay={150}>
              <div className="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow cursor-pointer border border-gray-100">
                <div className="text-3xl mb-3">🏠</div>
                <div className="text-sm font-medium text-gray-700">Real Estate</div>
              </div>
            </FadeInSection>
            <FadeInSection delay={200}>
              <div className="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow cursor-pointer border border-gray-100">
                <div className="text-3xl mb-3">💻</div>
                <div className="text-sm font-medium text-gray-700">SaaS</div>
              </div>
            </FadeInSection>
            <FadeInSection delay={250}>
              <div className="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow cursor-pointer border border-gray-100">
                <div className="text-3xl mb-3">🚚</div>
                <div className="text-sm font-medium text-gray-700">Logistics</div>
              </div>
            </FadeInSection>
            <FadeInSection delay={300}>
              <div className="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow cursor-pointer border border-gray-100">
                <div className="text-3xl mb-3">💻</div>
                <div className="text-sm font-medium text-gray-700">IT</div>
              </div>
            </FadeInSection>
            <FadeInSection delay={350}>
              <div className="bg-white rounded-lg p-6 text-center hover:shadow-md transition-shadow cursor-pointer border border-gray-100">
                <div className="text-3xl mb-3">🏭</div>
                <div className="text-sm font-medium text-gray-700">Manufacturing</div>
              </div>
            </FadeInSection>
          </div>
          <div className="text-center mt-8">
            <LinkButton href="/solutions" variant="primary">
              View All Solutions
            </LinkButton>
          </div>
        </div>
      </section>

      {/* Trust Markers */}
      <section className="py-20 text-white" style={{ backgroundColor: 'rgb(30, 64, 175)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Certificate */}
            <FadeInSection>
              <div className="text-center lg:text-left">
                <div className="bg-white rounded-lg p-8 inline-block relative">
                  {/* Header text */}
                  <div className="text-center mb-4">
                    <div className="text-sm text-blue-600 font-medium mb-2">
                      CERTIFICATE OF PARTNERSHIP
                    </div>
                    <div className="text-lg font-semibold text-gray-900">
                      Who we are
                    </div>
                  </div>

                  <img
                    src="/Crtifikat.webp"
                    alt="Kommo Partnership Certificate"
                    className="w-full max-w-md mx-auto"
                  />
                </div>
              </div>
            </FadeInSection>

            {/* Content */}
            <FadeInSection delay={200}>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Setmee is a certified partner of Kommo
                </h2>
                <p className="text-lg text-blue-100 mb-8">
                  We are a team of specialists specialising in workflow automation and Kommo-based sales. We are committed to solving complex problems and interesting cases.
                </p>

                {/* Stats */}
                <div className="grid grid-cols-2 gap-6 mb-8">
                  <div>
                    <div className="text-3xl font-bold text-orange-400 mb-1">10 YEARS</div>
                    <div className="text-sm text-blue-200">CRM IMPLEMENTATION EXPERIENCE</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-orange-400 mb-1">320+</div>
                    <div className="text-sm text-blue-200">OF SUCCESSFUL PROJECTS</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-orange-400 mb-1">4000+</div>
                    <div className="text-sm text-blue-200">MANAGERS WHO HAVE RECEIVED OUR TRAINING</div>
                  </div>
                  <div>
                    <div className="text-3xl font-bold text-orange-400 mb-1">30+</div>
                    <div className="text-sm text-blue-200">DEVELOPED INTEGRATIONS</div>
                  </div>
                </div>

                {/* Why choose Setmee */}
                <div className="bg-blue-700 rounded-lg p-6">
                  <h3 className="text-xl font-semibold mb-4">Why choose Setmee?</h3>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Certified Kommo partnership</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>

                    </div>
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Complex problem-solving expertise</span>
                    </div>
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Custom integration development</span>
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>


      {/* Final CTA */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <FadeInSection>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Ready to Boost Your Sales Team&apos;s Performance?
                </h2>
                <p className="text-xl text-gray-300 mb-8">
                  Sign up for a free CRM audit. We&apos;ll review your current setup and identify key areas for growth.
                </p>
              </div>
            </FadeInSection>
            <FadeInSection delay={200}>
              <div>
                <HomeContactForm
                  className="bg-white text-gray-900"
                />
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>
    </div>
  );
}
