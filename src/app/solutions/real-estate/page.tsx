import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';
import FadeInSection from '@/components/FadeInSection';
import RealEstateCalculator from '@/components/RealEstateCalculator';
import RealEstateResults from '@/components/RealEstateResults';
import RealEstateFunnel from '@/components/RealEstateFunnel';
import RealEstateTrust from '@/components/RealEstateTrust';
import AnimatedCTA from '@/components/AnimatedCTA';

export const metadata: Metadata = {
  title: 'Kommo CRM for Real Estate Agencies | Automated Property Sales | Setmee',
  description: 'Transform your real estate business with Kommo CRM. Automate client and property management, increase viewings by 40%, reduce routine tasks by 50%. Get demo today.',
  keywords: 'real estate CRM, automated property sales, client and property management, real estate automation, Kommo CRM real estate, property management system',
  openGraph: {
    title: 'Kommo CRM for Real Estate Agencies | Setmee',
    description: 'Automate your real estate sales process with Kommo CRM. Proven to increase property viewings by 40% and reduce routine tasks by 50%.',
    type: 'website',
  },
};

export default function RealEstatePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-600 via-red-600 to-pink-700 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-orange-500 bg-opacity-20 text-orange-100 rounded-full text-sm font-semibold">
                🏠 Real Estate CRM Solution
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for <span className="text-orange-200">Real Estate Agencies</span>
              </h1>
              <p className="text-xl md:text-2xl text-orange-100 mb-8 leading-relaxed">
                Automate client and property management, increase viewings by 40%,
                and reduce routine tasks by 50%. Ready solution for real estate professionals.
              </p>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className="flex items-center text-orange-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>+40% more property viewings</span>
                </div>
                <div className="flex items-center text-orange-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>50% less routine tasks</span>
                </div>
                <div className="flex items-center text-orange-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>95% CRM adoption rate</span>
                </div>
                <div className="flex items-center text-orange-100">
                  <span className="text-green-400 mr-3 text-xl">✓</span>
                  <span>Ready in 5-7 days</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <AnimatedCTA
                  variant="primary"
                  size="lg"
                  className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
                  shakeDelay={5000}
                >
                  Get Demo Access
                </AnimatedCTA>
                <Button
                  variant="outline"
                  size="lg"
                  className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4"
                >
                  Calculate ROI
                </Button>
              </div>
            </FadeInSection>

            {/* Right Content - Screenshot placeholder */}
            <FadeInSection delay={300} className="hidden lg:block">
              <div className="relative">
                <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-2xl p-8 border border-white border-opacity-20">
                  <div className="text-center text-white">
                    <div className="text-6xl mb-4">📱</div>
                    <h3 className="text-xl font-semibold mb-2">Kommo CRM Interface</h3>
                    <p className="text-orange-100 text-sm">
                      Real estate-optimized dashboard with property and client management
                    </p>
                  </div>
                </div>

                {/* Floating elements */}
                <div className="absolute -top-4 -right-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Live Demo Available
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-red-100 text-red-700 rounded-full text-sm font-semibold">
                ⚠️ Common Real Estate Challenges
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What Real Estate Problems Does Kommo CRM Solve?
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Address the most common pain points in real estate sales and property management
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📤</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Manual Property Listing</h3>
                <p className="text-gray-600 text-sm">Uploading properties to multiple platforms manually takes hours</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automated listing upload
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={350}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">📱</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Scattered Client Data</h3>
                <p className="text-gray-600 text-sm">Client information spread across different systems and notebooks</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Unified client & property database
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={500}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">⏰</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Missed Follow-ups</h3>
                <p className="text-gray-600 text-sm">Forgetting to call back leads and missing important appointments</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Automatic reminders & follow-ups
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={650}>
              <Card variant="outlined" className="text-center p-6 h-full hover:shadow-lg transition-shadow duration-300 border-red-200">
                <div className="text-4xl mb-4">🔄</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Generic Sales Process</h3>
                <p className="text-gray-600 text-sm">Using standard CRM not adapted for long real estate sales cycles</p>
                <div className="mt-4 text-green-600 text-sm font-medium">
                  → Real estate-optimized funnel
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Real Estate Sales Funnel */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <RealEstateFunnel />
        </div>
      </section>

      {/* Automation Scenarios */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold">
                🤖 Automation Scenarios
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Real Estate Automation with Kommo CRM
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the automation level that fits your business needs and technical requirements
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <FadeInSection delay={200}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-blue-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mr-4">
                    ⚡
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Basic Kommo Automation</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Website integration:</strong> Leads automatically enter CRM with manager assignment</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Instant notifications:</strong> SMS to client + reminder to agent within 2 minutes</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Document automation:</strong> PDF contracts and agreements generated automatically</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Follow-up sequences:</strong> Automated re-engagement after 3, 6, 12 months</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <p className="text-blue-800 text-sm font-medium">Perfect for: Small to medium agencies getting started with automation</p>
                </div>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="elevated" className="p-8 h-full border-l-4 border-l-purple-500">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 text-xl mr-4">
                    🚀
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900">Advanced with Make.com</h3>
                </div>
                <ul className="space-y-4 text-gray-700">
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Multi-platform listing:</strong> Properties uploaded to MLS, Zillow, Realtor.com automatically</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Smart matching:</strong> AI-powered client-property matching with instant notifications</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Market intelligence:</strong> Price change alerts and comparative market analysis</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span><strong>Financial integration:</strong> Mortgage calculator and pre-approval workflows</span>
                  </li>
                </ul>
                <div className="mt-6 p-4 bg-purple-50 rounded-lg">
                  <p className="text-purple-800 text-sm font-medium">Perfect for: Established agencies wanting maximum automation and efficiency</p>
                </div>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Interactive Calculator */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-12">
              <div className="inline-block px-4 py-2 mb-6 bg-orange-100 text-orange-700 rounded-full text-sm font-semibold">
                💰 ROI Calculator
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Calculate Your Potential Revenue Increase
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                See how much additional revenue you could generate with proper CRM automation
              </p>
            </div>
          </FadeInSection>

          <FadeInSection delay={300}>
            <RealEstateCalculator />
          </FadeInSection>
        </div>
      </section>

      {/* Results & Testimonials */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <RealEstateResults />
        </div>
      </section>

      {/* Trust Elements */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <RealEstateTrust />
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gradient-to-br from-orange-600 via-red-600 to-pink-700 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-orange-500 bg-opacity-20 text-orange-100 rounded-full text-sm font-semibold">
                  🚀 Get Started Today
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Ready to Transform Your <span className="text-orange-200">Real Estate Business</span>?
                </h2>
                <p className="text-xl md:text-2xl text-orange-100 mb-8 leading-relaxed">
                  Get a personalized demo of Kommo CRM configured specifically for real estate agencies.
                  See how automation can increase your revenue by 40% and save 50% of routine time.
                </p>

                {/* Benefits list */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                  <div className="flex items-center text-orange-100">
                    <span className="text-green-400 mr-3 text-xl">✓</span>
                    <span>Free 30-minute consultation</span>
                  </div>
                  <div className="flex items-center text-orange-100">
                    <span className="text-green-400 mr-3 text-xl">✓</span>
                    <span>Personalized demo setup</span>
                  </div>
                  <div className="flex items-center text-orange-100">
                    <span className="text-green-400 mr-3 text-xl">✓</span>
                    <span>ROI calculation for your agency</span>
                  </div>
                  <div className="flex items-center text-orange-100">
                    <span className="text-green-400 mr-3 text-xl">✓</span>
                    <span>Implementation roadmap</span>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row gap-4">
                  <AnimatedCTA
                    variant="primary"
                    size="lg"
                    className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
                    shakeDelay={3000}
                  >
                    Request Demo
                    <span className="ml-2">→</span>
                  </AnimatedCTA>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4"
                  >
                    Book Consultation
                  </Button>
                </div>

                <p className="text-orange-200 text-sm mt-4">
                  ⏱️ Setup takes 5-7 days • 💰 ROI typically achieved in 2-3 months
                </p>
              </div>
              <div>
                <FadeInSection delay={300}>
                  <ContactForm
                    title="Get Real Estate CRM Demo"
                    subtitle="Fill out the form and we'll show you how to increase your revenue by 40%"
                    buttonText="Get Demo"
                    className="bg-white text-gray-900 shadow-2xl"
                  />
                </FadeInSection>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
