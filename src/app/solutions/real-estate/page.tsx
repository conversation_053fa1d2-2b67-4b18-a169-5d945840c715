import React from 'react';
import Button from '@/components/Button';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';

export default function RealEstatePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-orange-600 via-red-600 to-pink-700 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Kommo CRM for Real Estate Agencies: Ready Solution
            </h1>
            <p className="text-xl text-orange-100 mb-8 max-w-4xl mx-auto">
              Automate work with properties, clients, and deals. Reduce deal cycle time and increase the number of closed contracts.
            </p>
          </div>
        </div>
      </section>

      {/* Problems Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              What Tasks Does It Solve?
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card variant="outlined" className="text-center p-6">
              <div className="text-4xl mb-4">📤</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Automatic Listing Upload</h3>
              <p className="text-gray-600 text-sm">Upload properties to platforms automatically</p>
            </Card>
            <Card variant="outlined" className="text-center p-6">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Unified Database</h3>
              <p className="text-gray-600 text-sm">Single property and client database, accessible from phone</p>
            </Card>
            <Card variant="outlined" className="text-center p-6">
              <div className="text-4xl mb-4">⏰</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Automatic Reminders</h3>
              <p className="text-gray-600 text-sm">Automatic reminders for calls and meetings</p>
            </Card>
            <Card variant="outlined" className="text-center p-6">
              <div className="text-4xl mb-4">🔄</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Adapted Sales Funnel</h3>
              <p className="text-gray-600 text-sm">Sales funnel adapted for long deal cycles</p>
            </Card>
          </div>
        </div>
      </section>

      {/* How Kommo Solves */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How Kommo Solves This
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card variant="elevated" className="text-center p-6">
              <div className="text-4xl mb-4">🏠</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Unified Property and Client Database</h3>
            </Card>
            <Card variant="elevated" className="text-center p-6">
              <div className="text-4xl mb-4">🔔</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Automatic Reminders and Follow-up</h3>
            </Card>
            <Card variant="elevated" className="text-center p-6">
              <div className="text-4xl mb-4">🔗</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Real Estate Platform Integrations</h3>
            </Card>
            <Card variant="elevated" className="text-center p-6">
              <div className="text-4xl mb-4">📊</div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Configured Reports by Managers and Funnel</h3>
            </Card>
          </div>
        </div>
      </section>

      {/* Individual Funnel */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Individual Funnel for Real Estate
            </h2>
            <p className="text-xl text-gray-600">
              Simple scheme or step description: from lead to contract
            </p>
          </div>
          
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8">
            <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  1
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Lead</h4>
                <p className="text-sm text-gray-600">Initial inquiry</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  2
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Qualification</h4>
                <p className="text-sm text-gray-600">Needs assessment</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  3
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Viewing</h4>
                <p className="text-sm text-gray-600">Property showing</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  4
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Negotiation</h4>
                <p className="text-sm text-gray-600">Price discussion</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                  5
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Documents</h4>
                <p className="text-sm text-gray-600">Paperwork</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto mb-3 bg-green-600 text-white rounded-full flex items-center justify-center font-bold">
                  ✓
                </div>
                <h4 className="font-semibold text-gray-900 mb-1">Deal Closed</h4>
                <p className="text-sm text-gray-600">Contract signed</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Automation Scenarios */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Automation Scenarios (Make or without it)
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card variant="elevated" className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Automation</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Website → Kommo → manager task</li>
                <li>• Inquiry → automatic SMS + reminder</li>
                <li>• Deal closed → PDF contract generation</li>
                <li>• Repeat interest after 3 months → auto message</li>
              </ul>
            </Card>
            <Card variant="elevated" className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Advanced with Make</h3>
              <ul className="space-y-2 text-gray-700">
                <li>• Auto property upload to multiple platforms</li>
                <li>• Price change notifications</li>
                <li>• Client matching with suitable properties</li>
                <li>• Integration with mortgage calculators</li>
              </ul>
            </Card>
          </div>
        </div>
      </section>

      {/* Interactive Calculator */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How Much Are You Losing on Unprocessed Leads?
            </h2>
          </div>
          
          <Card variant="elevated" className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Calculator</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Average leads per month
                    </label>
                    <input 
                      type="number" 
                      placeholder="50"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Current conversion to deal
                    </label>
                    <input 
                      type="number" 
                      placeholder="15"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-500">%</span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Average commission per deal
                    </label>
                    <input 
                      type="number" 
                      placeholder="5000"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <span className="text-sm text-gray-500">$</span>
                  </div>
                </div>
              </div>
              <div className="bg-blue-50 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-blue-900 mb-4">Potential Results with CRM:</h4>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-blue-700">Current monthly revenue:</span>
                    <span className="font-semibold text-blue-900">$37,500</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">With 25% conversion:</span>
                    <span className="font-semibold text-blue-900">$62,500</span>
                  </div>
                  <div className="border-t border-blue-200 pt-3">
                    <div className="flex justify-between">
                      <span className="text-blue-700 font-medium">Additional revenue:</span>
                      <span className="font-bold text-green-600 text-lg">+$25,000/month</span>
                    </div>
                  </div>
                </div>
                <Button variant="primary" className="w-full mt-6">
                  Get Demo Access to CRM
                </Button>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* Case Study */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Case Study or Implementation Result
            </h2>
          </div>
          
          <Card variant="elevated" className="p-8 max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
              <div>
                <div className="text-3xl font-bold text-green-600 mb-2">+40%</div>
                <p className="text-gray-700">More property viewings in 3 months</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-blue-600 mb-2">95%</div>
                <p className="text-gray-700">Managers now enter clients into CRM</p>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-600 mb-2">-50%</div>
                <p className="text-gray-700">Reduced time on routine tasks</p>
              </div>
            </div>
            
            <div className="mt-8 text-center">
              <blockquote className="text-lg text-gray-700 italic">
                Before Kommo, we lost about 30% of leads due to poor follow-up. Now every client is tracked,
                and we&apos;ve increased our closing rate significantly.
              </blockquote>
              <cite className="block mt-4 text-gray-600">— Real Estate Agency Owner</cite>
            </div>
          </Card>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Get Consultation or Demo Funnel for Your Business
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                See how Kommo CRM can transform your real estate business
              </p>
            </div>
            <div>
              <ContactForm
                title="Get Real Estate CRM Demo"
                subtitle=""
                buttonText="Get Demo"
                className="bg-white text-gray-900"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
