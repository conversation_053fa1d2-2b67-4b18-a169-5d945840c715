import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import Card from '@/components/Card';
import SolutionsContactForm from '@/components/SolutionsContactForm';

export const metadata: Metadata = {
  title: 'Kommo CRM Solutions by Industry | Real Estate, SaaS, B2B | Setmee',
  description: 'Industry-specific Kommo CRM solutions for real estate, SaaS, B2B sales, healthcare, and more. Ready automation scenarios for your business vertical.',
  keywords: 'Kommo CRM solutions, industry CRM, real estate CRM, B2B CRM, SaaS CRM, healthcare CRM',
  openGraph: {
    title: 'Kommo CRM Solutions by Industry | Setmee',
    description: 'Industry-specific Kommo CRM solutions with ready automation scenarios for your business vertical.',
    type: 'website',
  },
};

const industries = [
  {
    id: "real-estate",
    name: "Real Estate Agencies",
    description: "Long sales cycle, many calls, lost leads",
    automation: "Form integration, mailings, follow-up automation, MLS synchronization",
    icon: "🏠",
    href: "/solutions/real-estate"
  },
  {
    id: "digital-marketing",
    name: "Digital Marketing Agencies", 
    description: "Many clients, tasks, advertising channels",
    automation: "Lead collection, KPI report automation, data transfer to other systems",
    icon: "📱",
    href: "/solutions/digital-marketing"
  },
  {
    id: "coaching",
    name: "Coaching / Online Courses",
    description: "Leads from landing pages, subscriptions, engagement",
    automation: "LMS integration, email chains, reminders, payment tracking",
    icon: "🎓",
    href: "/solutions/coaching"
  },
  {
    id: "ecommerce",
    name: "eCommerce (B2B / Niche)",
    description: "Leads from advertising, custom orders",
    automation: "Order collection, notifications, ERP or Google Sheets integration",
    icon: "🛍️",
    href: "/solutions/ecommerce"
  },
  {
    id: "saas-tech",
    name: "SaaS & Tech Companies",
    description: "Website leads, trial subscriptions, metrics",
    automation: "Stripe integration, activity tracking, renewal reminders",
    icon: "💻",
    href: "/solutions/saas-tech"
  },
  {
    id: "healthcare",
    name: "Healthcare & Clinics",
    description: "Patient appointments, reminders, confidentiality",
    automation: "Auto-appointment booking, SMS/email reminders, calendar integration",
    icon: "🏥",
    href: "/solutions/healthcare"
  },
  {
    id: "legal",
    name: "Legal Services",
    description: "Website inquiries, long cycles",
    automation: "Auto-responses, follow-up, document uploads, deadlines",
    icon: "⚖️",
    href: "/solutions/legal"
  },
  {
    id: "recruiting",
    name: "Recruiting Agencies",
    description: "Vacancy/candidate funnel",
    automation: "Auto-responses, funnel stages, email chains",
    icon: "👥",
    href: "/solutions/recruiting"
  },
  {
    id: "construction",
    name: "Construction / Contractors",
    description: "B2B and B2C, offline inquiries",
    automation: "Website integration, notifications, calendar synchronization",
    icon: "🏗️",
    href: "/solutions/construction"
  },
  {
    id: "financial",
    name: "Financial Services",
    description: "Confidentiality, strict process",
    automation: "Reminders, document generation, stage tracking",
    icon: "💰",
    href: "/solutions/financial"
  },
  {
    id: "education",
    name: "Education / Language Schools",
    description: "Courses, lessons, CRM + email",
    automation: "Trial lesson booking, Zoom integration, reminders",
    icon: "📚",
    href: "/solutions/education"
  },
  {
    id: "custom",
    name: "Your Business",
    description: "Don't see your industry? We create custom solutions",
    automation: "Individual approach based on your specific needs",
    icon: "🎯",
    href: "/contact"
  }
];

export default function SolutionsPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-800 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Kommo CRM for Your Business
            </h1>
            <p className="text-xl text-indigo-100 mb-8 max-w-4xl mx-auto">
              We don&apos;t offer universal solutions. We configure Kommo considering your industry specifics:
              typical processes, key metrics, and customer characteristics. Choose your industry to see ready scenarios.
            </p>
          </div>
        </div>
      </section>

      {/* Industries Grid */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Choose Your Industry
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Each industry has its own specifics. We&apos;ve prepared ready solutions and automation scenarios
              for the most popular business areas.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {industries.map((industry) => (
              <Link key={industry.id} href={industry.href}>
                <Card variant="elevated" hover className="h-full p-6 group cursor-pointer">
                  <div className="text-center mb-4">
                    <div className="text-4xl mb-3">{industry.icon}</div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                      {industry.name}
                    </h3>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Why it&apos;s important / Problems:</h4>
                      <p className="text-sm text-gray-600">{industry.description}</p>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-700 mb-1">What can be automated:</h4>
                      <p className="text-sm text-gray-600">{industry.automation}</p>
                    </div>
                  </div>
                  
                  <div className="mt-6 text-center">
                    <span className="text-blue-600 text-sm font-medium group-hover:text-blue-700 transition-colors">
                      Learn More →
                    </span>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Don&apos;t See Your Industry?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                We create custom solutions for any business. Our team has experience with hundreds of different industries and processes.
              </p>
            </div>
            <div>
              <SolutionsContactForm className="bg-white text-gray-900" />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
