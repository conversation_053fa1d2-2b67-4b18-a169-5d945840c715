import React from 'react';
import { Metadata } from 'next';
import Link from 'next/link';
import Card from '@/components/Card';
import SolutionsContactForm from '@/components/SolutionsContactForm';
import IndustryCard from '@/components/IndustryCard';
import FadeInSection from '@/components/FadeInSection';

export const metadata: Metadata = {
  title: 'Kommo CRM Solutions by Industry | Real Estate, SaaS, B2B | Setmee',
  description: 'Industry-specific Kommo CRM solutions for real estate, SaaS, B2B sales, healthcare, and more. Ready automation scenarios for your business vertical.',
  keywords: 'Kommo CRM solutions, industry CRM, real estate CRM, B2B CRM, SaaS CRM, healthcare CRM',
  openGraph: {
    title: 'Kommo CRM Solutions by Industry | Setmee',
    description: 'Industry-specific Kommo CRM solutions with ready automation scenarios for your business vertical.',
    type: 'website',
  },
};

const industries = [
  {
    id: "real-estate",
    name: "Real Estate Agencies",
    description: "Long sales cycle, many calls, lost leads",
    automation: "Form integration, mailings, follow-up automation, MLS synchronization",
    icon: "🏠",
    href: "/solutions/real-estate",
    colorScheme: {
      gradient: "bg-gradient-to-br from-orange-500 to-red-600",
      button: "bg-orange-600 hover:bg-orange-700",
      buttonHover: "hover:bg-orange-700"
    }
  },
  {
    id: "digital-marketing",
    name: "Digital Marketing Agencies",
    description: "Many clients, tasks, advertising channels",
    automation: "Lead collection, KPI report automation, data transfer to other systems",
    icon: "📱",
    href: "/solutions/digital-marketing",
    colorScheme: {
      gradient: "bg-gradient-to-br from-blue-500 to-purple-600",
      button: "bg-blue-600 hover:bg-blue-700",
      buttonHover: "hover:bg-blue-700"
    }
  },
  {
    id: "coaching",
    name: "Coaching / Online Courses",
    description: "Leads from landing pages, subscriptions, engagement",
    automation: "LMS integration, email chains, reminders, payment tracking",
    icon: "🎓",
    href: "/solutions/coaching",
    colorScheme: {
      gradient: "bg-gradient-to-br from-green-500 to-emerald-600",
      button: "bg-green-600 hover:bg-green-700",
      buttonHover: "hover:bg-green-700"
    }
  },
  {
    id: "ecommerce",
    name: "eCommerce (B2B / Niche)",
    description: "Leads from advertising, custom orders",
    automation: "Order collection, notifications, ERP or Google Sheets integration",
    icon: "🛍️",
    href: "/solutions/ecommerce",
    colorScheme: {
      gradient: "bg-gradient-to-br from-purple-500 to-pink-600",
      button: "bg-purple-600 hover:bg-purple-700",
      buttonHover: "hover:bg-purple-700"
    }
  },
  {
    id: "saas-tech",
    name: "SaaS & Tech Companies",
    description: "Website leads, trial subscriptions, metrics",
    automation: "Stripe integration, activity tracking, renewal reminders",
    icon: "💻",
    href: "/solutions/saas-tech",
    colorScheme: {
      gradient: "bg-gradient-to-br from-blue-600 to-indigo-700",
      button: "bg-indigo-600 hover:bg-indigo-700",
      buttonHover: "hover:bg-indigo-700"
    }
  },
  {
    id: "healthcare",
    name: "Healthcare & Clinics",
    description: "Patient appointments, reminders, confidentiality",
    automation: "Auto-appointment booking, SMS/email reminders, calendar integration",
    icon: "🏥",
    href: "/solutions/healthcare",
    colorScheme: {
      gradient: "bg-gradient-to-br from-cyan-500 to-blue-600",
      button: "bg-cyan-600 hover:bg-cyan-700",
      buttonHover: "hover:bg-cyan-700"
    }
  },
  {
    id: "legal",
    name: "Legal Services",
    description: "Website inquiries, long cycles",
    automation: "Auto-responses, follow-up, document uploads, deadlines",
    icon: "⚖️",
    href: "/solutions/legal",
    colorScheme: {
      gradient: "bg-gradient-to-br from-slate-600 to-gray-700",
      button: "bg-slate-700 hover:bg-slate-800",
      buttonHover: "hover:bg-slate-800"
    }
  },
  {
    id: "recruiting",
    name: "Recruiting Agencies",
    description: "Vacancy/candidate funnel",
    automation: "Auto-responses, funnel stages, email chains",
    icon: "👥",
    href: "/solutions/recruiting",
    colorScheme: {
      gradient: "bg-gradient-to-br from-teal-500 to-cyan-600",
      button: "bg-teal-600 hover:bg-teal-700",
      buttonHover: "hover:bg-teal-700"
    }
  },
  {
    id: "construction",
    name: "Construction / Contractors",
    description: "B2B and B2C, offline inquiries",
    automation: "Website integration, notifications, calendar synchronization",
    icon: "🏗️",
    href: "/solutions/construction",
    colorScheme: {
      gradient: "bg-gradient-to-br from-yellow-600 to-orange-700",
      button: "bg-yellow-700 hover:bg-yellow-800",
      buttonHover: "hover:bg-yellow-800"
    }
  },
  {
    id: "financial",
    name: "Financial Services",
    description: "Confidentiality, strict process",
    automation: "Reminders, document generation, stage tracking",
    icon: "💰",
    href: "/solutions/financial",
    colorScheme: {
      gradient: "bg-gradient-to-br from-green-600 to-emerald-700",
      button: "bg-green-700 hover:bg-green-800",
      buttonHover: "hover:bg-green-800"
    }
  },
  {
    id: "education",
    name: "Education / Language Schools",
    description: "Courses, lessons, CRM + email",
    automation: "Trial lesson booking, Zoom integration, reminders",
    icon: "📚",
    href: "/solutions/education",
    colorScheme: {
      gradient: "bg-gradient-to-br from-indigo-500 to-purple-600",
      button: "bg-indigo-600 hover:bg-indigo-700",
      buttonHover: "hover:bg-indigo-700"
    }
  },
  {
    id: "custom",
    name: "Your Business",
    description: "Don't see your industry? We create custom solutions",
    automation: "Individual approach based on your specific needs",
    icon: "🎯",
    href: "/contact",
    colorScheme: {
      gradient: "bg-gradient-to-br from-gray-500 to-gray-600",
      button: "bg-white text-gray-800 hover:bg-gray-100",
      buttonHover: "hover:bg-gray-100"
    }
  }
];

export default function SolutionsPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-indigo-600 via-purple-700 to-pink-800 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="text-center text-white">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Kommo CRM for Your <span className="text-pink-200">Business</span>
              </h1>
              <p className="text-xl md:text-2xl text-indigo-100 mb-8 max-w-4xl mx-auto leading-relaxed">
                We don&apos;t offer universal solutions. We configure Kommo considering your industry specifics:
                typical processes, key metrics, and customer characteristics. Choose your industry to see ready scenarios.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-3xl mx-auto">
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-pink-200">12+</div>
                  <div className="text-sm text-indigo-200">Industries</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-pink-200">320+</div>
                  <div className="text-sm text-indigo-200">Projects</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-pink-200">10+</div>
                  <div className="text-sm text-indigo-200">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-pink-200">4000+</div>
                  <div className="text-sm text-indigo-200">Managers Trained</div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
                <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">🎯</span>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Industry-Specific</h3>
                <p className="text-gray-600 text-sm">Tailored solutions for your business vertical</p>
              </div>

              <div className="text-center p-6 bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl border border-green-100">
                <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">⚡</span>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Ready Scenarios</h3>
                <p className="text-gray-600 text-sm">Pre-built automation workflows</p>
              </div>

              <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl border border-purple-100">
                <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-2xl">🚀</span>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Quick Start</h3>
                <p className="text-gray-600 text-sm">Implementation in 5-7 days</p>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Industries Grid */}
      <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-indigo-100 text-indigo-700 rounded-full text-sm font-semibold">
                🎯 Industry Solutions
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Choose Your <span className="text-indigo-600">Industry</span>
              </h2>
              <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
                Each industry has its own specifics. We&apos;ve prepared ready solutions and automation scenarios
                for the most popular business areas.
              </p>

              {/* Subtitle */}
              <div className="mt-8 p-6 bg-white rounded-xl shadow-lg max-w-2xl mx-auto border border-gray-100">
                <p className="text-gray-700 font-medium">
                  <span className="text-indigo-600 font-bold">✨ Ready-to-use scenarios</span> •
                  <span className="text-green-600 font-bold ml-2">🚀 Quick implementation</span> •
                  <span className="text-purple-600 font-bold ml-2">📈 Proven results</span>
                </p>
              </div>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8">
            {industries.map((industry, index) => (
              <FadeInSection key={industry.id} delay={index * 100}>
                <IndustryCard
                  industry={industry}
                  delay={index * 100}
                />
              </FadeInSection>
            ))}
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-20"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-40 h-40 bg-indigo-500 opacity-10 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-32 h-32 bg-purple-500 opacity-10 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-indigo-600 bg-opacity-20 text-indigo-200 rounded-full text-sm font-semibold">
                  🎯 Custom Solutions
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
                  Don&apos;t See Your <span className="text-indigo-300">Industry</span>?
                </h2>
                <p className="text-xl md:text-2xl text-gray-300 mb-8 leading-relaxed">
                  We create custom solutions for any business. Our team has experience with hundreds of different industries and processes.
                </p>

                {/* Features list */}
                <div className="space-y-4 mb-8">
                  <div className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-3">✅</span>
                    <span>Individual approach to your business specifics</span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-3">✅</span>
                    <span>Experience with 100+ different industries</span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <span className="text-green-400 mr-3">✅</span>
                    <span>Custom automation scenarios development</span>
                  </div>
                </div>
              </div>
              <div>
                <SolutionsContactForm className="bg-white text-gray-900 shadow-2xl" />
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
