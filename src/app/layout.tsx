import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import StructuredData from "@/components/StructuredData";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Setmee - Certified Kommo CRM Partner | Implementation & Optimization",
  description: "Expert Kommo CRM implementation, optimization, and integrations. Certified partner helping businesses automate sales processes and grow revenue.",
  keywords: "Kommo CRM, CRM implementation, sales automation, business optimization, Make integrations",
  authors: [{ name: "<PERSON><PERSON>" }],
  icons: {
    icon: '/favicon.ico',
    apple: '/favicon.ico',
  },
  openGraph: {
    title: "Setmee - Certified Kommo CRM Partner",
    description: "Expert Kommo CRM implementation, optimization, and integrations for business growth.",
    type: "website",
    locale: "en_US",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <StructuredData type="organization" />
        <StructuredData type="service" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
