import React from 'react';
import { Metadata } from 'next';
import Button from '@/components/Button';
import Card from '@/components/Card';
import FadeInSection from '@/components/FadeInSection';
import TeamSection from '@/components/TeamSection';
import CompanyHistory from '@/components/CompanyHistory';
import CertificatesSection from '@/components/CertificatesSection';
import { teamInfo, competencies, advantages, achievements, missionValues } from '@/data/content';

export const metadata: Metadata = {
  title: 'About Setmee - Certified Kommo CRM Partner | 10+ Years Experience',
  description: 'Learn about Setmee: certified Kommo CRM partner with 10+ years of experience, 320+ successful projects, and 4000+ trained managers. Meet our expert team and discover our mission.',
  keywords: 'Setmee company, Kommo certified partner, CRM experts, sales automation team, business digitization, CRM consulting, about us',
  openGraph: {
    title: 'About Setmee - Certified Kommo CRM Partner',
    description: 'Certified Kommo CRM partner with 10+ years of experience. Meet our expert team and learn about our mission to transform businesses through CRM automation.',
    type: 'website',
  },
};

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white opacity-5 rounded-full"></div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <FadeInSection>
            <div className="text-center text-white">
              <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
                🏢 About Setmee
              </div>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Your Trusted Partner in <span className="text-blue-200">CRM Excellence</span>
              </h1>
              <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-4xl mx-auto leading-relaxed">
                Since 2015, we've been transforming businesses through expert CRM implementation
                and automation. We don't just provide tools—we build lasting partnerships that drive real growth.
              </p>

              {/* Key Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12 max-w-3xl mx-auto">
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-blue-200">10</div>
                  <div className="text-sm text-blue-300">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-blue-200">320+</div>
                  <div className="text-sm text-blue-300">Successful Projects</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-blue-200">4000+</div>
                  <div className="text-sm text-blue-300">Managers Trained</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl md:text-3xl font-bold text-blue-200">98%</div>
                  <div className="text-sm text-blue-300">Client Satisfaction</div>
                </div>
              </div>

              <div className="mt-8">
                <Button
                  variant="primary"
                  size="lg"
                  className="bg-white !text-blue-600 hover:bg-gray-100 hover:!text-blue-700 px-8 py-4 text-lg font-semibold"
                >
                  Learn More About Us
                </Button>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Company History and Values */}
      <CompanyHistory />

      {/* Expertise and Facts */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-green-100 text-green-700 rounded-full text-sm font-semibold">
                📊 Our Expertise
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Proven Results and Deep Expertise
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our track record speaks for itself. Here are the facts and figures that demonstrate
                our commitment to delivering exceptional results for our clients.
              </p>
            </div>
          </FadeInSection>

          {/* Enhanced Stats */}
          <FadeInSection delay={200}>
            <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 mb-16 border border-blue-100">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">10</div>
                  <div className="text-sm text-gray-600 font-medium uppercase tracking-wide">Years CRM Experience</div>
                  <div className="text-xs text-gray-500 mt-1">Since 2015</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">320+</div>
                  <div className="text-sm text-gray-600 font-medium uppercase tracking-wide">Successful Projects</div>
                  <div className="text-xs text-gray-500 mt-1">Across 12+ industries</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">4000+</div>
                  <div className="text-sm text-gray-600 font-medium uppercase tracking-wide">Managers Trained</div>
                  <div className="text-xs text-gray-500 mt-1">Active CRM users</div>
                </div>
                <div>
                  <div className="text-4xl font-bold text-blue-600 mb-2">98%</div>
                  <div className="text-sm text-gray-600 font-medium uppercase tracking-wide">Client Satisfaction</div>
                  <div className="text-xs text-gray-500 mt-1">Based on surveys</div>
                </div>
              </div>
            </div>
          </FadeInSection>

          {/* Key Points */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {teamInfo.points.map((point, index) => (
              <FadeInSection key={index} delay={400 + index * 150}>
                <Card variant="outlined" className="p-6 h-full hover:shadow-lg transition-shadow duration-300">
                  <div className="flex items-start space-x-4">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-full flex items-center justify-center font-bold text-sm flex-shrink-0 mt-1">
                      {index + 1}
                    </div>
                    <p className="text-gray-700 leading-relaxed">{point}</p>
                  </div>
                </Card>
              </FadeInSection>
            ))}
          </div>
        </div>
      </section>

      {/* Key Competencies */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-purple-100 text-purple-700 rounded-full text-sm font-semibold">
                🎯 Core Competencies
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What We Do Best
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our comprehensive expertise covers every aspect of CRM implementation and automation,
                ensuring your business gets the most effective and scalable solutions.
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {competencies.map((competency, index) => (
              <FadeInSection key={index} delay={200 + index * 150}>
                <Card variant="elevated" className="p-8 h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-lg flex items-center justify-center font-bold text-lg flex-shrink-0">
                      {index + 1}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-3">{competency.title}</h3>
                      <p className="text-gray-600 leading-relaxed">{competency.description}</p>
                    </div>
                  </div>
                </Card>
              </FadeInSection>
            ))}
          </div>
        </div>
      </section>

      {/* Certificates and Partnerships */}
      <CertificatesSection />

      {/* Team Section */}
      <TeamSection />

      {/* Why Choose Us */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <div className="inline-block px-4 py-2 mb-6 bg-green-100 text-green-700 rounded-full text-sm font-semibold">
                ✨ Why Choose Setmee
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Your Success is Our Commitment
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Our methodology and approach ensure measurable results and long-term partnership
                based on transparency, professionalism, and genuine care for your business growth.
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {advantages.map((advantage, index) => (
              <FadeInSection key={index} delay={200 + index * 150}>
                <Card variant="outlined" className="p-6 h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 bg-green-100 text-green-600 rounded-full flex items-center justify-center flex-shrink-0">
                      <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-3">{advantage.title}</h3>
                      <p className="text-gray-600 leading-relaxed">{advantage.description}</p>
                    </div>
                  </div>
                </Card>
              </FadeInSection>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Decorative elements */}
        <div className="absolute top-10 right-10 w-32 h-32 bg-white opacity-5 rounded-full"></div>
        <div className="absolute bottom-10 left-10 w-24 h-24 bg-white opacity-10 rounded-full"></div>

        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <FadeInSection>
            <div className="inline-block px-4 py-2 mb-6 bg-blue-500 bg-opacity-20 text-blue-100 rounded-full text-sm font-semibold">
              🤝 Let's Connect
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 leading-tight">
              Ready to Start a <span className="text-blue-200">Conversation</span>?
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-8 leading-relaxed max-w-3xl mx-auto">
              We'd love to learn about your business and explore how we can help you achieve your goals.
              No pressure, no sales pitch—just an honest conversation about your needs and our expertise.
            </p>

            {/* Contact Options */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 max-w-2xl mx-auto">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center text-blue-200 text-xl mx-auto mb-2">
                  📞
                </div>
                <div className="text-sm text-blue-200">Free Consultation</div>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center text-blue-200 text-xl mx-auto mb-2">
                  💬
                </div>
                <div className="text-sm text-blue-200">No Sales Pressure</div>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center text-blue-200 text-xl mx-auto mb-2">
                  🎯
                </div>
                <div className="text-sm text-blue-200">Honest Advice</div>
              </div>
            </div>

            <div className="flex justify-center">
              <Button
                variant="primary"
                size="lg"
                className="bg-blue-600 text-white hover:bg-blue-700 hover:text-white px-8 py-4 text-lg font-semibold border-0 shadow-lg"
              >
                Start a Conversation
              </Button>
            </div>

            <p className="text-blue-200 text-sm mt-6">
              ⏱️ Typical response time: Within 2 hours during business hours
            </p>
          </FadeInSection>
        </div>
      </section>
    </div>
  );
}
