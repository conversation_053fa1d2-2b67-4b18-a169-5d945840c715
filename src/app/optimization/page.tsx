import React from 'react';
import { Metadata } from 'next';

import ContactForm from '@/components/ContactForm';
import SectionHeading from '@/components/SectionHeading';
import FeatureCard from '@/components/FeatureCard';


import FadeInSection from '@/components/FadeInSection';
import AnimatedCT<PERSON> from '@/components/AnimatedCTA';
import WaveDivider from '@/components/WaveDivider';


import AccordionFAQ from '@/components/AccordionFAQ';
import CertifiedPartnerSection from '@/components/CertifiedPartnerSection';


export const metadata: Metadata = {
  title: 'Kommo CRM Optimization & Audit | Increase Conversion by 30% | Setmee',
  description: 'Professional Kommo CRM optimization and audit. Fix errors, increase conversion, reduce routine work. Get maximum ROI from your existing CRM system.',
  keywords: 'Kommo CRM optimization, CRM audit, sales conversion, CRM efficiency, Kommo consulting',
  openGraph: {
    title: 'Kommo CRM Optimization & Audit | Setmee',
    description: 'Fix errors, increase conversion, reduce routine work. Get maximum ROI from your existing CRM system.',
    type: 'website',
  },
};

export default function OptimizationPage() {
  // FAQ data for optimization
  const optimizationFAQ = [

    {
      id: '1',
      question: 'How long does CRM optimization take?',
      answer: 'Typically, 5–8 business days, depending on the scope of work and the current state of your system. If your CRM is heavily loaded or requires major changes, we can break the work into stages.'
    },
    {
      id: '2',
      question: 'Can we skip the audit?',
      answer: 'No. The audit is 80% of success. Without a thorough analysis, we\'d be working blindly. We examine your system, identify bottlenecks, and only then suggest specific solutions.'
    },
    {
      id: '3',
      question: 'Do you work only with Kommo CRM?',
      answer: 'Yes. We specialize exclusively in Kommo (formerly amoCRM) and know its strengths and weaknesses in detail. This allows us to solve tasks faster and more effectively.'
    },
    {
      id: '4',
      question: 'What if another contractor already set up our CRM?',
      answer: 'We often work with "inherited" setups. No criticism—we simply audit, adjust, and improve. If something is done well, we keep it.'
    },
    {
      id: '5',
      question: 'What if we already have some automation in place?',
      answer: 'Great! We won\'t start from scratch. We\'ll check what\'s working, then recommend improvements or optimization for what you have.'
    },
    {
      id: '6',
      question: 'Can we order just the audit without further work?',
      answer: 'Yes. We can conduct only the audit and provide a report with recommendations. After that, you decide whether to implement the changes yourself, with us, or with another partner.'
    },

    {
      id: '8',
      question: 'What results can we expect?',
      answer: 'Typical outcomes: – Conversion growth of 5–30% – Increased manager engagement – Reduced manual routine – Clear and transparent sales pipeline – Reduced system resistance'
    },
    {
      id: '9',
      question: 'What do you need from us?',
      answer: '– Admin access to Kommo CRM – Appoint a contact person from your team – Participation in a short interview during the audit phase – Willingness to train staff after optimization'
    },
    {
      id: '10',
      question: 'How deeply do you get involved in business processes?',
      answer: 'As deeply as necessary for high-quality setup. We don\'t just "flip switches" in the interface—we ask about your sales logic, clients, goals, and metrics. We tailor the CRM to real processes, not the other way around.'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-green-600 via-green-700 to-emerald-800 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                Kommo CRM <span className="text-green-300">Optimization</span>: Get <span className="text-orange-400">Maximum</span> from Your System
              </h1>
              <p className="text-xl text-green-100 mb-8 leading-relaxed">
                We&apos;ll conduct a deep audit of your existing Kommo CRM, identify bottlenecks, fix errors,
                and configure it to actually <span className="font-semibold text-white">increase your sales by 30%</span> and reduce routine work.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="shadow-xl px-8 py-4 bg-white text-gray-800 hover:bg-gray-100 hover:text-gray-900 hover:shadow-2xl transition-all duration-300 border-0"
                  shakeDelay={10000}
                >
                  Get Free CRM Audit
                </AnimatedCTA>
                <div className="flex items-center text-green-200 text-sm">
                  <span className="mr-2">⚡</span>
                  <span>Results in 1-2 weeks</span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center text-green-200">
                  <span className="mr-2">📊</span>
                  <span>Deep CRM Audit</span>
                </div>
                <div className="flex items-center text-green-200">
                  <span className="mr-2">⚡</span>
                  <span>Performance Boost</span>
                </div>
                <div className="flex items-center text-green-200">
                  <span className="mr-2">🎯</span>
                  <span>Sales Optimization</span>
                </div>
              </div>
            </FadeInSection>

            {/* Right Visual - CRM Optimization Dashboard */}
            <FadeInSection delay={200} className="relative">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-8 border border-white border-opacity-20">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    Kommo CRM Optimization Dashboard
                  </h3>
                  <p className="text-sm text-green-200">Audit & Performance Analysis</p>
                </div>

                {/* Audit Progress Steps */}
                <div className="flex justify-between items-center mb-8">
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      ✓
                    </div>
                    <span className="text-xs text-green-200 text-center">Audit</span>
                  </div>
                  <div className="flex-1 h-0.5 bg-green-400 mx-2"></div>
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      2
                    </div>
                    <span className="text-xs text-green-200 text-center">Optimize</span>
                  </div>
                  <div className="flex-1 h-0.5 bg-green-400 mx-2"></div>
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      3
                    </div>
                    <span className="text-xs text-green-200 text-center">Results</span>
                  </div>
                </div>

                {/* Performance Metrics */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-green-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Conversion Rate</div>
                    <div className="text-xs text-green-200">+30% ↗️</div>
                  </div>
                  <div className="bg-blue-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Efficiency</div>
                    <div className="text-xs text-green-200">+60% ⚡</div>
                  </div>
                  <div className="bg-purple-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Data Quality</div>
                    <div className="text-xs text-green-200">95% ✓</div>
                  </div>
                  <div className="bg-orange-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Team Satisfaction</div>
                    <div className="text-xs text-green-200">90%+ 😊</div>
                  </div>
                </div>

                <div className="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                  <p className="text-white text-sm font-medium">
                    🔍 Ready to optimize your CRM performance?
                  </p>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Who This Service Is For */}
      <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Perfect For"
            title="Who This Service Is For"
            subtitle="If you recognize yourself in at least one situation - optimization will bring noticeable results"
            accentWords={["Service"]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            <FadeInSection delay={0}>
              <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">💸</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Lost Leads</h3>
                    <p className="text-gray-600">You&apos;re losing leads because the system is not configured correctly.</p>
                  </div>
                </div>
              </div>
            </FadeInSection>

            <FadeInSection delay={200}>
              <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">👥</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Team Resistance</h3>
                    <p className="text-gray-600">Your CRM is implemented, but your team either avoids using it or actively resists.</p>
                  </div>
                </div>
              </div>
            </FadeInSection>

            <FadeInSection delay={400}>
              <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">📊</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Poor Analytics</h3>
                    <p className="text-gray-600">Reports fail to provide a clear picture, making it hard to track KPIs.</p>
                  </div>
                </div>
              </div>
            </FadeInSection>

            <FadeInSection delay={600}>
              <div className="bg-white rounded-xl p-6 shadow-lg border border-blue-100 hover:shadow-xl transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">⏰</span>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">Time Waste</h3>
                    <p className="text-gray-600">Routine tasks consume time that could be spent on sales.</p>
                  </div>
                </div>
              </div>
            </FadeInSection>
          </div>


          <FadeInSection delay={800} className="mt-16">
            <div className="bg-gradient-to-br from-blue-600 to-indigo-700 rounded-2xl p-8 text-white relative overflow-hidden">
              {/* Background decoration */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
              <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full translate-y-12 -translate-x-12"></div>

              <div className="relative z-10">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-white/20 rounded-full mb-4">
                    <span className="text-3xl">🔍</span>
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold mb-2">
                    CRM Health Check
                  </h3>
                  <p className="text-blue-100 text-lg">
                    Does Your System Need <span className="text-orange-300 font-semibold">Optimization</span>?
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                    <h4 className="font-semibold text-white mb-4 flex items-center">
                      <span className="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                      System Issues
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <span className="text-orange-300 mr-3 mt-1 text-sm">●</span>
                        <span className="text-blue-100 text-sm">Funnels look cumbersome but don&apos;t work</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-orange-300 mr-3 mt-1 text-sm">●</span>
                        <span className="text-blue-100 text-sm">CRM exists, but you still control everything manually</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-orange-300 mr-3 mt-1 text-sm">●</span>
                        <span className="text-blue-100 text-sm">Team complains about complexity or doesn&apos;t use the system</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                    <h4 className="font-semibold text-white mb-4 flex items-center">
                      <span className="w-2 h-2 bg-orange-400 rounded-full mr-3"></span>
                      Business Impact
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-start">
                        <span className="text-orange-300 mr-3 mt-1 text-sm">●</span>
                        <span className="text-blue-100 text-sm">You don&apos;t see where leads are lost</span>
                      </div>
                      <div className="flex items-start">
                        <span className="text-orange-300 mr-3 mt-1 text-sm">●</span>
                        <span className="text-blue-100 text-sm">Reports look nice but provide no value for decisions</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-green-500/20 border border-green-400/30 rounded-xl p-6 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <span className="text-green-300 text-2xl mr-3">✓</span>
                    <span className="text-white font-semibold text-lg">
                      If you recognize at least one point - optimization will likely bring
                      <span className="text-green-300 font-bold"> noticeable results</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Who This Service Is NOT For */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <span className="inline-block px-4 py-2 bg-blue-100 text-blue-600 rounded-full text-sm font-medium mb-4">
                Important Notice
              </span>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Who This Service Is <span className="text-blue-600">NOT</span> For
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                To save your time and ours, please read this carefully
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FadeInSection delay={0}>
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg mb-4">
                  <span className="text-2xl">🔒</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">No CRM Access</h3>
                <p className="text-gray-600 leading-relaxed">
                  You don&apos;t have access to your CRM or are unwilling to make changes to it.
                </p>
              </div>
            </FadeInSection>

            <FadeInSection delay={200}>
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg mb-4">
                  <span className="text-2xl">👥</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">No Team Involvement</h3>
                <p className="text-gray-600 leading-relaxed">
                  You expect results without any involvement from your team.
                </p>
              </div>
            </FadeInSection>

            <FadeInSection delay={400}>
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200 hover:shadow-md transition-all duration-300">
                <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-lg mb-4">
                  <span className="text-2xl">⚠️</span>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">Wrong CRM Platform</h3>
                <p className="text-gray-600 leading-relaxed">
                  You don&apos;t use Kommo CRM (though we do offer migration services for other platforms).
                </p>
              </div>
            </FadeInSection>
          </div>

          <FadeInSection delay={600} className="mt-12">
            <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 text-center">
              <div className="flex items-center justify-center mb-3">
                <span className="text-2xl mr-3">💡</span>
                <h3 className="text-lg font-semibold text-gray-900">Perfect Fit?</h3>
              </div>
              <p className="text-gray-700">
                If none of the above applies to you, then our CRM optimization service could be exactly what you need!
              </p>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Common Mistakes */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Common Issues"
            title="Common Mistakes in Kommo CRM Setup"
            subtitle="These are the most frequent problems we encounter and fix"
            accentWords={["Mistakes", "Setup"]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FadeInSection delay={0}>
              <FeatureCard
                icon="🔄"
                title="CRM-Centric Pipelines"
                description="Pipelines are built around CRM logic, not actual sales processes."
                borderColor="red"
                iconColor="text-orange-500"
                hover={true}
              />
            </FadeInSection>

            <FadeInSection delay={200}>
              <FeatureCard
                icon="📞"
                title="Missing Automation"
                description="Automated tasks are missing—so managers forget to make follow-up calls."
                borderColor="red"
                iconColor="text-orange-500"
                hover={true}
              />
            </FadeInSection>

            <FadeInSection delay={400}>
              <FeatureCard
                icon="👥"
                title="No Lead Segmentation"
                description="All leads are lumped together without segmentation."
                borderColor="red"
                iconColor="text-orange-500"
                hover={true}
              />
            </FadeInSection>

            <FadeInSection delay={600}>
              <FeatureCard
                icon="🔄"
                title="Duplicate Tracking Issues"
                description="Duplicate requests aren&apos;t tracked."
                borderColor="red"
                iconColor="text-orange-500"
                hover={true}
              />
            </FadeInSection>

            <FadeInSection delay={800}>
              <FeatureCard
                icon="📊"
                title="Unrealistic Metrics"
                description="Metrics don&apos;t reflect reality."
                borderColor="red"
                iconColor="text-orange-500"
                hover={true}
              />
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Project Timeline */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Step by Step"
            title="How the Project Works"
            subtitle="Clear timeline with specific deliverables at each stage"
            accentWords={["Project", "Works"]}
          />

          <FadeInSection className="mb-16">
            <div className="bg-white rounded-2xl p-8 max-w-6xl mx-auto shadow-lg border border-gray-100">
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full mb-4">
                  <span className="text-white text-2xl">🔍</span>
                </div>
                <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                  What&apos;s included in your Kommo CRM audit
                </h3>
                <p className="text-gray-600 text-lg max-w-2xl mx-auto">
                  Before changing anything, we conduct comprehensive diagnostics on key points
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <span className="text-orange-500 text-lg">⚙️</span>
                    </div>
                    <h4 className="font-semibold text-gray-900">Technical Analysis</h4>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <span className="w-2 h-2 bg-orange-400 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                      <span className="text-gray-700 text-sm">Current funnel analysis: logic, stages, overload</span>
                    </div>
                    <div className="flex items-start">
                      <span className="w-2 h-2 bg-orange-400 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                      <span className="text-gray-700 text-sm">Auto-task, template, and trigger verification</span>
                    </div>
                    <div className="flex items-start">
                      <span className="w-2 h-2 bg-orange-400 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                      <span className="text-gray-700 text-sm">Integration error analysis: website, email, messengers, telephony</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-xl p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                      <span className="text-orange-500 text-lg">👥</span>
                    </div>
                    <h4 className="font-semibold text-gray-900">Team & Process Analysis</h4>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <span className="w-2 h-2 bg-orange-400 rounded-full mr-3 mt-2 flex-shrink-0"></span>
                      <span className="text-gray-700 text-sm">Team activity assessment: bottlenecks, what&apos;s being sabotaged</span>
                    </div>                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6">
                <div className="flex items-center justify-center text-center">
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-4">
                      <span className="text-green-600 text-xl">🎯</span>
                    </div>
                    <div>
                      <p className="text-gray-800 font-semibold text-lg">
                        <span className="text-green-600">Result:</span> you get a clear list of problems + priority action plan with ROI forecast
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </FadeInSection>

          {/* Interactive Process Visualization */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - CRM Dashboard Mockup */}
            <FadeInSection delay={200}>
              <div className="relative">
                <div className="bg-white rounded-2xl shadow-2xl p-6 border border-gray-100">
                  {/* Browser Header */}
                  <div className="flex items-center mb-4 pb-3 border-b border-gray-200">
                    <div className="flex space-x-2">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    <div className="flex-1 text-center">
                      <div className="bg-blue-500 text-white px-4 py-1 rounded-lg text-sm font-medium inline-block">
                        Kommo CRM Dashboard
                      </div>
                    </div>
                  </div>

                  {/* Dashboard Content */}
                  <div className="space-y-4">
                    {/* Stats Cards */}
                    <div className="grid grid-cols-4 gap-3">
                      <div className="bg-green-50 p-3 rounded-lg text-center">
                        <div className="text-xs text-green-600 mb-1">New Leads</div>
                        <div className="text-lg font-bold text-green-700">24</div>
                      </div>
                      <div className="bg-orange-50 p-3 rounded-lg text-center">
                        <div className="text-xs text-orange-600 mb-1">Qualifying</div>
                        <div className="text-lg font-bold text-orange-700">18</div>
                      </div>
                      <div className="bg-blue-50 p-3 rounded-lg text-center">
                        <div className="text-xs text-blue-600 mb-1">Proposal</div>
                        <div className="text-lg font-bold text-blue-700">12</div>
                      </div>
                      <div className="bg-purple-50 p-3 rounded-lg text-center">
                        <div className="text-xs text-purple-600 mb-1">Closed</div>
                        <div className="text-lg font-bold text-purple-700">8</div>
                      </div>
                    </div>

                    {/* Analytics Chart */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <div className="text-sm font-medium text-gray-700 mb-2">Analytics</div>
                      <div className="flex items-end space-x-1 h-16">
                        <div className="bg-blue-400 w-4 h-8 rounded-t"></div>
                        <div className="bg-blue-400 w-4 h-12 rounded-t"></div>
                        <div className="bg-blue-400 w-4 h-6 rounded-t"></div>
                        <div className="bg-blue-400 w-4 h-16 rounded-t"></div>
                        <div className="bg-blue-400 w-4 h-10 rounded-t"></div>
                        <div className="bg-blue-400 w-4 h-14 rounded-t"></div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">Conversion Rate: 32%</div>
                    </div>

                    {/* Audit Status */}
                    <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm font-medium text-green-700">✓ Audit Report Ready</span>
                      </div>
                      <div className="text-xs text-green-600 mt-1 ml-4">
                        • Funnel settings analyzed<br/>
                        • Technical errors identified<br/>
                        • Analytics optimized
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>

            {/* Right side - Process Steps */}
            <FadeInSection delay={400}>
              <div className="space-y-6">
                {/* Step 1 - Diagnostics and Audit */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">🔍</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-blue-50 border-l-4 border-blue-500 rounded-r-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-blue-900">Diagnostics and Audit</h4>
                        <span className="text-xs bg-blue-200 text-blue-800 px-2 py-1 rounded-full font-medium">1-2 days</span>
                      </div>
                      <p className="text-blue-700 text-sm mb-3">
                        Deep analysis of current settings, funnels, fields, and integrations. Interviews with key users and managers.
                      </p>
                      <div className="text-xs text-blue-600 space-y-1">
                        <div>• Current funnel analysis: logic, stages, overload</div>
                        <div>• Auto-task, template, and trigger verification</div>
                        <div>• Integration error analysis: website, email, messengers, telephony</div>
                        <div>• Team activity assessment: bottlenecks, what&apos;s being sabotaged</div>
                        <div>• Report and actual data comparison</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 2 - Optimization Plan Development */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">📋</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-purple-50 border-l-4 border-purple-500 rounded-r-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-purple-900">Optimization Plan Development</h4>
                        <span className="text-xs bg-purple-200 text-purple-800 px-2 py-1 rounded-full font-medium">1 day</span>
                      </div>
                      <p className="text-purple-700 text-sm mb-3">
                        Report compilation with identified problems. Specific solutions and implementation roadmap proposal.
                      </p>
                      <div className="text-xs text-purple-600 space-y-1">
                        <div>• Detailed problem analysis report</div>
                        <div>• Priority action plan creation</div>
                        <div>• ROI forecast for each improvement</div>
                        <div>• Implementation timeline planning</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 3 - Change Implementation */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">⚙️</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-green-50 border-l-4 border-green-500 rounded-r-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-green-900">Change Implementation</h4>
                        <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded-full font-medium">3-5 days</span>
                      </div>
                      <p className="text-green-700 text-sm mb-3">
                        Settings correction, error fixes, new automation implementation. Funnel and business process optimization within CRM.
                      </p>
                      <div className="text-xs text-green-600 space-y-1">
                        <div>• Funnel structure optimization</div>
                        <div>• Automation setup and testing</div>
                        <div>• Integration fixes and improvements</div>
                        <div>• Custom field and pipeline configuration</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 4 - Testing and Training */}
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xl">🎓</span>
                  </div>
                  <div className="flex-1">
                    <div className="bg-orange-50 border-l-4 border-orange-500 rounded-r-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-semibold text-orange-900">Testing and Training</h4>
                        <span className="text-xs bg-orange-200 text-orange-800 px-2 py-1 rounded-full font-medium">1 day</span>
                      </div>
                      <p className="text-orange-700 text-sm mb-3">
                        System functionality verification after changes. Additional team training on new features and optimized processes.
                      </p>
                      <div className="text-xs text-orange-600 space-y-1">
                        <div>• Full system functionality testing</div>
                        <div>• Team training on new processes</div>
                        <div>• Documentation and guidelines creation</div>
                        <div>• Support and maintenance setup</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Final Result */}
                <div className="mt-8 bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded-xl p-6">
                  <div className="flex items-center justify-center mb-3">
                    <div className="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                      <span className="text-white text-lg">🚀</span>
                    </div>
                    <h3 className="text-xl font-bold text-orange-900">Final Result</h3>
                  </div>
                  <p className="text-orange-800 font-medium text-center">
                    CRM that works efficiently, brings <span className="text-orange-600 font-bold">more sales</span>, and makes your team <span className="text-orange-600 font-bold">happy</span>
                  </p>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Trust Markers */}
      <section className="py-16" style={{ backgroundColor: 'rgb(30, 64, 175)' }}>
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Trusted by Companies
            </h2>
            <p className="text-lg text-blue-100">
              Join hundreds of businesses that have optimized their CRM with us
            </p>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <FadeInSection delay={100}>
              <div className="text-center text-white">
                <div className="text-lg font-semibold mb-1">🏅 Official Kommo Partner</div>
              </div>
            </FadeInSection>

            <FadeInSection delay={200}>
              <div className="text-center text-white">
                <div className="text-lg font-semibold mb-1">📈 Proven Track Record</div>
              </div>
            </FadeInSection>

            <FadeInSection delay={300}>
              <div className="text-center text-white">
                <div className="text-lg font-semibold mb-1">🤝 Multi-Industry Experience</div>
              </div>
            </FadeInSection>

            <FadeInSection delay={400}>
              <div className="text-center text-white">
                <div className="text-lg font-semibold mb-1">🔒 Result Guarantee</div>
              </div>
            </FadeInSection>
          </div>

          {/* Stats */}
          <FadeInSection delay={500}>
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
                <div>
                  <div className="text-3xl font-bold text-orange-400 mb-1">10+</div>
                  <div className="text-white text-sm">Years Experience</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-orange-400 mb-1">320+</div>
                  <div className="text-white text-sm">Projects Completed</div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-orange-400 mb-1">4000+</div>
                  <div className="text-white text-sm">Managers Trained</div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Performance Improvements */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Performance Metrics"
            title="Typical Optimization Results"
            subtitle="See how our optimization improves key business metrics"
            accentWords={["Optimization", "Results"]}
            size="md"
          />

          {/* Modern Metrics Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <FadeInSection delay={100}>
              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 border border-green-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center">
                    <span className="text-white text-xl">📈</span>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-green-700">85%</div>
                    <div className="text-xs text-green-600 font-medium">+25% improvement</div>
                  </div>
                </div>
                <h3 className="font-semibold text-green-900 mb-1">Lead Conversion Rate</h3>
                <p className="text-green-700 text-sm">Better funnel optimization leads to higher conversion</p>
              </div>
            </FadeInSection>

            <FadeInSection delay={200}>
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 border border-blue-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center">
                    <span className="text-white text-xl">⚡</span>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-blue-700">92%</div>
                    <div className="text-xs text-blue-600 font-medium">+60% efficiency</div>
                  </div>
                </div>
                <h3 className="font-semibold text-blue-900 mb-1">Team Productivity</h3>
                <p className="text-blue-700 text-sm">Automation saves 3+ hours per manager daily</p>
              </div>
            </FadeInSection>

            <FadeInSection delay={300}>
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 border border-purple-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center">
                    <span className="text-white text-xl">🎯</span>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-purple-700">95%</div>
                    <div className="text-xs text-purple-600 font-medium">+40% accuracy</div>
                  </div>
                </div>
                <h3 className="font-semibold text-purple-900 mb-1">Data Accuracy</h3>
                <p className="text-purple-700 text-sm">Clean data structure and validation rules</p>
              </div>
            </FadeInSection>

            <FadeInSection delay={400}>
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-2xl p-6 border border-orange-200 hover:shadow-lg transition-all duration-300">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-orange-500 rounded-xl flex items-center justify-center">
                    <span className="text-white text-xl">😊</span>
                  </div>
                  <div className="text-right">
                    <div className="text-3xl font-bold text-orange-700">88%</div>
                    <div className="text-xs text-orange-600 font-medium">+35% satisfaction</div>
                  </div>
                </div>
                <h3 className="font-semibold text-orange-900 mb-1">User Satisfaction</h3>
                <p className="text-orange-700 text-sm">Teams love working with optimized systems</p>
              </div>
            </FadeInSection>
          </div>

          {/* Summary Banner */}
          <FadeInSection delay={500}>
            <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-2xl p-8 text-center">
              <div className="flex items-center justify-center mb-4">
                <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-500 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white text-2xl">🚀</span>
                </div>
                <div className="text-left">
                  <h3 className="text-2xl font-bold text-white mb-1">Average Improvement</h3>
                  <p className="text-gray-300">Across all key metrics within first month</p>
                </div>
              </div>
              <div className="text-6xl font-bold text-transparent bg-gradient-to-r from-orange-400 to-orange-500 bg-clip-text mb-2">
                30-40%
              </div>
              <p className="text-gray-300 text-lg max-w-2xl mx-auto">
                Our optimization delivers measurable results that directly impact your bottom line and team satisfaction
              </p>
            </div>
          </FadeInSection>
        </div>
      </section>

      <WaveDivider color="white" />

      {/* Mid-page CTA */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-indigo-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <FadeInSection className="text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Need a deep audit of your <span className="text-blue-200">Kommo CRM</span>?
            </h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
              Get a comprehensive analysis of your current setup and a personalized optimization roadmap
            </p>
            <AnimatedCTA
              variant="primary"
              size="lg"
              className="shadow-xl px-10 py-4 text-lg font-semibold"
              shakeDelay={8000}
            >
              Book Free Audit
            </AnimatedCTA>
            <div className="mt-6 flex items-center justify-center text-blue-200 text-sm">
              <span className="mr-2">⏱️</span>
              <span>Takes 30 minutes • Results in 24 hours</span>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Results"
            title="What You&apos;ll Get in the End"
            subtitle="Measurable improvements that directly impact your bottom line"
            accentWords={["Get", "End"]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <FadeInSection delay={0}>
              <FeatureCard
                icon="📈"
                title="Increased Conversion"
                description="Fewer lost leads, more closed deals. Average improvement: 5-30%"
                variant="elevated"
                borderColor="green"
                iconColor="text-green-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={200}>
              <FeatureCard
                icon="⚡"
                title="Improved Manager Efficiency"
                description="Eliminate routine, focus on sales. Save 2-4 hours per day per manager"
                variant="elevated"
                borderColor="blue"
                iconColor="text-blue-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={400}>
              <FeatureCard
                icon="📊"
                title="Accurate Analytics Data"
                description="Transparent reports and real control. Make data-driven decisions"
                variant="elevated"
                borderColor="purple"
                iconColor="text-purple-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={600}>
              <FeatureCard
                icon="❤️"
                title="Reduced System Sabotage"
                description="Managers will love and actively use CRM. 90%+ adoption rate"
                variant="elevated"
                borderColor="red"
                iconColor="text-red-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={800}>
              <FeatureCard
                icon="💰"
                title="Cost Optimization"
                description="Maximum return on CRM investment. ROI typically 300-500%"
                variant="elevated"
                borderColor="green"
                iconColor="text-green-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Certified Partner Section */}
      <CertifiedPartnerSection variant="blue" />

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="FAQ"
            title="Frequently Asked Questions"
            subtitle="Everything you need to know about Kommo CRM optimization"
            accentWords={["Questions"]}
          />

          <FadeInSection>
            <AccordionFAQ
              items={optimizationFAQ}
              variant="bordered"
              allowMultiple={false}
              animated={true}
            />
          </FadeInSection>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <FadeInSection>
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-green-500 bg-opacity-20 text-green-300 rounded-full text-sm font-semibold">
                  🎯 Free Consultation
                </div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6 leading-tight">
                  Get Your Personalized <span className="text-green-400">Kommo CRM</span> Optimization Plan
                </h2>
                <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                  Fill out the form and get a personalized optimization roadmap with ROI forecast within 24 hours
                </p>
                <div className="space-y-4 text-gray-300">
                  <div className="flex items-center">
                    <span className="text-green-400 mr-3">✓</span>
                    <span>Free CRM health check and audit</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 mr-3">✓</span>
                    <span>Personalized optimization roadmap</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 mr-3">✓</span>
                    <span>ROI forecast and timeline</span>
                  </div>
                </div>
              </div>
            </FadeInSection>
            <FadeInSection delay={200}>
              <div className="bg-white rounded-xl shadow-2xl p-2">
                <ContactForm
                  title="Get Optimization Plan"
                  subtitle=""
                  showGift={true}
                  giftText="✅ Yes, I&apos;d like to receive a free &apos;CRM Self-Diagnosis Checklist&apos; as a bonus."
                  buttonText="Submit Application"
                  className="bg-white text-gray-900"
                />
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>
    </div>
  );
}
