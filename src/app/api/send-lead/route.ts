import { NextRequest, NextResponse } from 'next/server';

// Helper function to generate HTML for PDF with personalization
function generatePdfHtml(results: Record<string, unknown>, name: string, businessData: Record<string, unknown> = {}): string {
  const { niche, managers, channels } = businessData;
  const nicheSpecificContent = getNicheSpecificContent(niche as string);
  const caseStudyQuote = getCaseStudyQuote(niche as string);

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>ROI Calculation Report - ${niche || 'Business'} Analysis</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          margin: 0;
          padding: 40px;
          line-height: 1.6;
          color: #333;
        }
        .header {
          text-align: center;
          margin-bottom: 40px;
          padding: 30px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 10px;
        }
        .metric {
          margin: 20px 0;
          padding: 25px;
          border: 1px solid #e1e5e9;
          border-radius: 8px;
          background: #f8f9fa;
        }
        .highlight { color: #2563eb; font-weight: bold; font-size: 1.2em; }
        .niche-header {
          background: #e3f2fd;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #2196f3;
        }
        .case-study {
          background: #f3e5f5;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          font-style: italic;
          border-left: 4px solid #9c27b0;
        }
        .justification {
          background: #e8f5e8;
          padding: 15px;
          border-radius: 8px;
          font-size: 0.9em;
          border-left: 4px solid #4caf50;
        }
        .footer {
          text-align: center;
          margin-top: 40px;
          padding: 20px;
          background: #f5f5f5;
          border-radius: 8px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>ROI Calculation Report</h1>
        ${niche ? `<h2>${niche as string} Industry Analysis</h2>` : ''}
        <p>Prepared for: ${name}</p>
        <p>Date: ${new Date().toLocaleDateString()}</p>
      </div>

      ${niche ? `
      <div class="niche-header">
        <h2>Industry-Specific Analysis: ${niche as string}</h2>
        <p>${nicheSpecificContent}</p>
      </div>
      ` : ''}

      <div class="metric">
        <h2>📈 Monthly Revenue Increase</h2>
        <p class="highlight">$${results?.deltaRevenue ? new Intl.NumberFormat('en-US').format(Math.round(results.deltaRevenue as number)) : '0'}</p>
        <p>Based on your current metrics: ${(results as any)?.initialData?.leads || 0} leads/month, ${(results as any)?.initialData?.conversion || 0}% conversion rate</p>
      </div>

      <div class="metric">
        <h2>🎯 Conversion Rate Improvement</h2>
        <p class="highlight">+${(results as any)?.conversionGrowth || 0}%</p>
        <p>From ${(results as any)?.initialData?.conversion || 0}% to ${(((results as any)?.initialData?.conversion || 0) * 1.25).toFixed(1)}%</p>
        <div class="justification">
          <strong>Why +25% conversion?</strong><br>
          Based on data from 100+ automation projects in Kommo and Make.com from 2023–2025.
          The average conversion increase with automation is 20% to 30%.
        </div>
      </div>

      ${(results as any)?.savedHours ? `
      <div class="metric">
        <h2>⏰ Time Saved</h2>
        <p class="highlight">${(results as any).savedHours} hours/month</p>
        ${(managers as number) > 1 ? `<p>Across ${managers as number} managers, this represents significant resource optimization.</p>` : ''}
      </div>
      ` : ''}

      ${channels && Array.isArray(channels) && channels.length > 0 ? `
      <div class="metric">
        <h2>📊 Channel Optimization</h2>
        <p>Your current channels: ${(channels as string[]).join(', ')}</p>
        <p>Automation can optimize lead processing across all these channels for maximum efficiency.</p>
      </div>
      ` : ''}

      ${caseStudyQuote ? `
      <div class="case-study">
        <h3>Success Story from ${niche as string}</h3>
        <p>${caseStudyQuote}</p>
      </div>
      ` : ''}

      <div class="metric">
        <h2>📋 Next Steps</h2>
        <ol>
          <li><strong>Schedule a consultation</strong> - Let's discuss your specific business needs</li>
          <li><strong>Custom automation audit</strong> - We'll analyze your current processes</li>
          <li><strong>Implementation roadmap</strong> - Get a detailed plan tailored to your business</li>
          <li><strong>ROI tracking</strong> - Monitor your results and optimize further</li>
        </ol>
      </div>

      <div class="footer">
        <h2>Ready to Transform Your Business?</h2>
        <p><strong>Contact us today:</strong></p>
        <p>📧 Email: <EMAIL></p>
        <p>🌐 Website: setmee.com</p>
        <p>💬 Let's discuss how automation can specifically benefit your ${(niche as string) || 'business'}</p>

        <p style="margin-top: 30px; font-size: 0.9em; color: #666;">
          *Calculations are approximate. Actual results depend on business specifics and current processes.
        </p>
      </div>
    </body>
    </html>
  `;
}

// Helper functions for personalization
function getNicheSpecificContent(niche: string): string {
  const nicheContent: Record<string, string> = {
    'Real Estate': 'In real estate, speed of response is crucial. Automation can help you be the first to contact leads, significantly improving your conversion rates.',
    'SaaS': 'SaaS businesses benefit from automated onboarding sequences and lead nurturing that can dramatically improve trial-to-paid conversion rates.',
    'Online Education': 'Educational platforms see great results from automated course recommendations and student engagement sequences.',
    'E-commerce': 'E-commerce automation excels in abandoned cart recovery, personalized product recommendations, and customer retention.',
    'Healthcare': 'Healthcare automation helps with appointment scheduling, patient follow-ups, and treatment plan adherence.',
    'Financial Services': 'Financial services benefit from automated lead qualification and compliance-friendly communication sequences.',
  };

  return nicheContent[niche] || 'Automation can significantly improve your lead processing and customer communication workflows.';
}

function getCaseStudyQuote(niche: string): string {
  const caseQuotes: Record<string, string> = {
    'Real Estate': '"Automation allowed us to process twice as many leads without increasing staff. This is a breakthrough!" - Anna Smirnova, Sales Director',
    'SaaS': '"Our response time improved from hours to minutes. Customer satisfaction and conversion rates skyrocketed." - Michael Chen, Head of Sales',
    'Online Education': '"Automated follow-ups helped us nurture leads better. Our course enrollment increased by 50%." - Sarah Johnson, Marketing Manager',
    'E-commerce': '"Abandoned cart recovery and automated email sequences transformed our business. ROI was immediate." - David Rodriguez, E-commerce Manager',
  };

  return caseQuotes[niche] || '';
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    if (!body.name || !body.email) {
      return NextResponse.json(
        { error: 'Name and email are required' },
        { status: 400 }
      );
    }

    // Prepare data for Make.com webhook
    const webhookData = {
      // Lead information
      name: body.name,
      email: body.email,
      phone: body.phone || '',
      comment: body.comment || '',
      wantsPdfReport: body.wantsPdfReport || false,

      // ROI calculation results
      calculationResults: body.calculationResults || null,

      // Additional business data (v2.2)
      businessData: {
        niche: body.calculationResults?.initialData?.niche || '',
        managers: body.calculationResults?.initialData?.managers || 0,
        channels: body.calculationResults?.initialData?.channels || [],
        leads: body.calculationResults?.initialData?.leads || 0,
        conversion: body.calculationResults?.initialData?.conversion || 0,
        avgDealSize: body.calculationResults?.initialData?.avgDealSize || 0,
        timePerLead: body.calculationResults?.initialData?.timePerLead || 0,
      },

      // PDF generation data (for Make.com)
      pdfData: body.wantsPdfReport ? {
        htmlContent: generatePdfHtml(body.calculationResults, body.name, {
          niche: body.calculationResults?.initialData?.niche || '',
          managers: body.calculationResults?.initialData?.managers || 0,
          channels: body.calculationResults?.initialData?.channels || [],
        }),
        fileName: `ROI_Report_${body.name?.replace(/\s+/g, '_') || 'User'}_${new Date().toISOString().split('T')[0]}.pdf`,
        subject: `Your ROI Calculation Report - ${body.calculationResults?.deltaRevenue ? `$${Math.round(body.calculationResults.deltaRevenue as number).toLocaleString()}` : ''} Monthly Increase Potential`,
      } : null,

      // UTM tracking data
      utmData: body.utmData || {},

      // Metadata
      timestamp: body.timestamp || new Date().toISOString(),
      source: 'roi-calculator-v2.2',
      userAgent: request.headers.get('user-agent') || '',
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || '',
    };

    // Send to Make.com webhook
    // Replace with your actual Make.com webhook URL
    const makeWebhookUrl = process.env.MAKE_WEBHOOK_URL;
    
    if (makeWebhookUrl) {
      const makeResponse = await fetch(makeWebhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookData),
      });

      if (!makeResponse.ok) {
        console.error('Failed to send data to Make.com:', makeResponse.statusText);
        // Don't fail the request if Make.com is down, just log the error
      }
    } else {
      console.warn('MAKE_WEBHOOK_URL environment variable not set');
    }

    // For development/testing, log the data
    if (process.env.NODE_ENV === 'development') {
      console.log('Lead captured:', {
        name: webhookData.name,
        email: webhookData.email,
        calculationResults: webhookData.calculationResults,
      });
    }

    return NextResponse.json(
      { 
        success: true, 
        message: 'Lead captured successfully' 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error processing lead:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
