import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  return NextResponse.json({
    message: 'Webhook test endpoint is working',
    timestamp: new Date().toISOString(),
    webhookUrl: process.env.MAKE_WEBHOOK_URL || 'NOT_SET',
  });
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔍 Testing webhook with data:', {
      name: body.name || 'Test User',
      email: body.email || '<EMAIL>',
      timestamp: new Date().toISOString(),
    });
    
    // Test data for webhook
    const testData = {
      name: body.name || 'Test User',
      email: body.email || '<EMAIL>',
      phone: body.phone || '+1234567890',
      comment: body.comment || 'Test webhook submission',
      wantsPdfReport: true,
      calculationResults: {
        deltaRevenue: 10000,
        gainPercent: 25,
        savedHours: 40,
      },
      businessData: {
        niche: 'Test',
        managers: 5,
        channels: ['email', 'website-form'],
      },
      utmData: {},
      timestamp: new Date().toISOString(),
      source: 'webhook-test',
      userAgent: request.headers.get('user-agent') || '',
      ip: request.headers.get('x-forwarded-for') || 'test-ip',
    };

    // Send to Make.com webhook
    const makeWebhookUrl = process.env.MAKE_WEBHOOK_URL;
    
    if (!makeWebhookUrl) {
      return NextResponse.json({
        success: false,
        error: 'MAKE_WEBHOOK_URL not configured',
        testData,
      });
    }

    console.log('🚀 Sending to webhook:', makeWebhookUrl);

    const makeResponse = await fetch(makeWebhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    });

    const responseText = await makeResponse.text();
    
    console.log('📥 Webhook response:', {
      status: makeResponse.status,
      statusText: makeResponse.statusText,
      headers: Object.fromEntries(makeResponse.headers.entries()),
      body: responseText,
    });

    if (makeResponse.ok) {
      return NextResponse.json({
        success: true,
        message: 'Webhook test successful',
        webhookResponse: {
          status: makeResponse.status,
          body: responseText,
        },
        testData,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: `Webhook failed: ${makeResponse.status} ${makeResponse.statusText}`,
        webhookResponse: {
          status: makeResponse.status,
          body: responseText,
        },
        testData,
      }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ Webhook test error:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}
