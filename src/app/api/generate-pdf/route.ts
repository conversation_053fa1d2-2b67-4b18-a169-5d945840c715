import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required data
    if (!body.calculationResults || !body.email) {
      return NextResponse.json(
        { error: 'Calculation results and email are required' },
        { status: 400 }
      );
    }

    // Extract business data for personalization
    const businessData = {
      niche: body.calculationResults?.initialData?.niche || '',
      managers: body.calculationResults?.initialData?.managers || 0,
      channels: body.calculationResults?.initialData?.channels || [],
    };

    // Generate personalized HTML content
    const htmlContent = generatePdfHtml(body.calculationResults, body.name || 'Valued Customer', businessData);

    // For now, return the HTML content for preview
    // In a full implementation, you would use puppeteer-core to generate PDF
    // Example implementation:
    /*
    const puppeteer = require('puppeteer-core');

    const browser = await puppeteer.launch({
      executablePath: '/path/to/chrome', // or use chrome-aws-lambda for serverless
    });

    const page = await browser.newPage();
    await page.setContent(htmlContent);
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: { top: '20px', bottom: '20px', left: '20px', right: '20px' }
    });

    await browser.close();

    // Send PDF via email or return as download
    */

    // For development, log the request and return HTML preview
    console.log('PDF generation requested for:', body.email);
    console.log('Business data:', businessData);

    return NextResponse.json(
      {
        success: true,
        message: 'PDF generation initiated',
        htmlPreview: htmlContent,
        businessData: businessData,
        note: 'PDF generation is ready for implementation with puppeteer-core'
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Helper function to generate HTML for PDF with personalization
function generatePdfHtml(results: Record<string, unknown>, name: string, businessData: Record<string, unknown> = {}): string {
  const { niche, managers, channels } = businessData;
  const nicheSpecificContent = getNicheSpecificContent(niche as string);
  const caseStudyQuote = getCaseStudyQuote(niche as string);

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>ROI Calculation Report - ${niche || 'Business'} Analysis</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          margin: 0;
          padding: 40px;
          line-height: 1.6;
          color: #333;
        }
        .header {
          text-align: center;
          margin-bottom: 40px;
          padding: 30px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 10px;
        }
        .metric {
          margin: 20px 0;
          padding: 25px;
          border: 1px solid #e1e5e9;
          border-radius: 8px;
          background: #f8f9fa;
        }
        .highlight { color: #2563eb; font-weight: bold; font-size: 1.2em; }
        .niche-header {
          background: #e3f2fd;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          border-left: 4px solid #2196f3;
        }
        .case-study {
          background: #f3e5f5;
          padding: 20px;
          border-radius: 8px;
          margin: 20px 0;
          font-style: italic;
          border-left: 4px solid #9c27b0;
        }
        .justification {
          background: #e8f5e8;
          padding: 15px;
          border-radius: 8px;
          font-size: 0.9em;
          border-left: 4px solid #4caf50;
        }
        .footer {
          text-align: center;
          margin-top: 40px;
          padding: 20px;
          background: #f5f5f5;
          border-radius: 8px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>ROI Calculation Report</h1>
        ${niche ? `<h2>${niche as string} Industry Analysis</h2>` : ''}
        <p>Prepared for: ${name}</p>
        <p>Date: ${new Date().toLocaleDateString()}</p>
      </div>

      ${niche ? `
      <div class="niche-header">
        <h2>Industry-Specific Analysis: ${niche as string}</h2>
        <p>${nicheSpecificContent}</p>
      </div>
      ` : ''}

      <div class="metric">
        <h2>📈 Monthly Revenue Increase</h2>
        <p class="highlight">$${(results.deltaRevenue as number)?.toLocaleString() || 0}</p>
        <p>Based on your current metrics: ${(results.initialData as any)?.leads || 0} leads/month, ${(results.initialData as any)?.conversion || 0}% conversion rate</p>
      </div>

      <div class="metric">
        <h2>🎯 Conversion Rate Improvement</h2>
        <p class="highlight">+${(results.conversionGrowth as number) || 0}%</p>
        <p>From ${(results.initialData as any)?.conversion || 0}% to ${(((results.initialData as any)?.conversion || 0) * 1.25).toFixed(1)}%</p>
        <div class="justification">
          <strong>Why +25% conversion?</strong><br>
          Based on data from 100+ automation projects in Kommo and Make.com from 2023–2025.
          The average conversion increase with automation is 20% to 30%.
        </div>
      </div>

      ${(results.savedHours as number) ? `
      <div class="metric">
        <h2>⏰ Time Saved</h2>
        <p class="highlight">${results.savedHours as number} hours/month</p>
        ${(managers as number) > 1 ? `<p>Across ${managers as number} managers, this represents significant resource optimization.</p>` : ''}
      </div>
      ` : ''}

      ${channels && Array.isArray(channels) && channels.length > 0 ? `
      <div class="metric">
        <h2>📊 Channel Optimization</h2>
        <p>Your current channels: ${(channels as string[]).join(', ')}</p>
        <p>Automation can optimize lead processing across all these channels for maximum efficiency.</p>
      </div>
      ` : ''}

      ${caseStudyQuote ? `
      <div class="case-study">
        <h3>Success Story from ${niche as string}</h3>
        <p>${caseStudyQuote}</p>
      </div>
      ` : ''}

      <div class="metric">
        <h2>📋 Next Steps</h2>
        <ol>
          <li><strong>Schedule a consultation</strong> - Let's discuss your specific business needs</li>
          <li><strong>Custom automation audit</strong> - We'll analyze your current processes</li>
          <li><strong>Implementation roadmap</strong> - Get a detailed plan tailored to your business</li>
          <li><strong>ROI tracking</strong> - Monitor your results and optimize further</li>
        </ol>
      </div>

      <div class="footer">
        <h2>Ready to Transform Your Business?</h2>
        <p><strong>Contact us today:</strong></p>
        <p>📧 Email: <EMAIL></p>
        <p>🌐 Website: setmee.com</p>
        <p>💬 Let's discuss how automation can specifically benefit your ${(niche as string) || 'business'}</p>

        <p style="margin-top: 30px; font-size: 0.9em; color: #666;">
          *Calculations are approximate. Actual results depend on business specifics and current processes.
        </p>
      </div>
    </body>
    </html>
  `;
}

// Helper functions for personalization
function getNicheSpecificContent(niche: string): string {
  const nicheContent: Record<string, string> = {
    'Real Estate': 'In real estate, speed of response is crucial. Automation can help you be the first to contact leads, significantly improving your conversion rates.',
    'SaaS': 'SaaS businesses benefit from automated onboarding sequences and lead nurturing that can dramatically improve trial-to-paid conversion rates.',
    'Online Education': 'Educational platforms see great results from automated course recommendations and student engagement sequences.',
    'E-commerce': 'E-commerce automation excels in abandoned cart recovery, personalized product recommendations, and customer retention.',
    'Healthcare': 'Healthcare automation helps with appointment scheduling, patient follow-ups, and treatment plan adherence.',
    'Financial Services': 'Financial services benefit from automated lead qualification and compliance-friendly communication sequences.',
  };

  return nicheContent[niche] || 'Automation can significantly improve your lead processing and customer communication workflows.';
}

function getCaseStudyQuote(niche: string): string {
  const caseQuotes: Record<string, string> = {
    'Real Estate': '"Automation allowed us to process twice as many leads without increasing staff. This is a breakthrough!" - Anna Smirnova, Sales Director',
    'SaaS': '"Our response time improved from hours to minutes. Customer satisfaction and conversion rates skyrocketed." - Michael Chen, Head of Sales',
    'Online Education': '"Automated follow-ups helped us nurture leads better. Our course enrollment increased by 50%." - Sarah Johnson, Marketing Manager',
    'E-commerce': '"Abandoned cart recovery and automated email sequences transformed our business. ROI was immediate." - David Rodriguez, E-commerce Manager',
  };

  return caseQuotes[niche] || '';
}
