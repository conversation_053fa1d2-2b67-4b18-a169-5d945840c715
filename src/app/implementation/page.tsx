import React from 'react';
import { Metadata } from 'next';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';

import FeatureCard from '@/components/FeatureCard';

import AccordionFAQ from '@/components/AccordionFAQ';
import FadeInSection from '@/components/FadeInSection';
import AnimatedCTA from '@/components/AnimatedCTA';
import CertifiedPartnerSection from '@/components/CertifiedPartnerSection';
import PricingPlans from '@/components/PricingPlans';
import PricingSchema from '@/components/PricingSchema';

export const metadata: Metadata = {
  title: 'Kommo CRM Implementation Pricing | $1,200-$6,000+ | Setmee - Certified Partner',
  description: 'Transparent Kommo CRM implementation pricing: Basic ($1,200), Standard ($3,000), Enterprise ($6,000+). Professional setup, automation, and training from certified partners.',
  keywords: 'Kommo CRM implementation, CRM setup pricing, CRM automation for business, Kommo partner, sales automation, CRM implementation cost',
  openGraph: {
    title: 'Kommo CRM Implementation Pricing | Transparent Plans | Setmee',
    description: 'Professional Kommo CRM implementation with transparent pricing. Choose from Basic, Standard, or Enterprise plans. Free audit included.',
    type: 'website',
  },
};

export default function ImplementationPage() {




  // FAQ data for implementation
  const implementationFAQ = [
    {
      id: '1',
      question: 'How long does it take to implement Kommo CRM?',
      answer: 'On average, it takes 7 to 30 days, depending on the complexity of your business processes, number of channels, and required integrations. Simple projects can be completed within a week; complex ones may take up to a month.'
    },
    {
      id: '2',
      question: 'When will we see the first results?',
      answer: 'Usually, you’ll notice improvements within the first week after launch: automation starts working, leads are no longer lost, and managers have clear tasks and deal stages. Within a month, you’ll see increased conversion and transparency in your sales department.'
    },
    {
      id: '3',
      question: 'What do you need from our team?',
      answer: 'At the start, we require the involvement of a responsible manager for interviews and structure approval. After that, we handle everything: setup, integrations, training, and support.'
    },
    {
      id: '4',
      question: 'Can you implement Kommo if we already have a CRM?',
      answer: 'Yes. We frequently work with companies that already have a CRM. We conduct an audit, identify issues, optimize pipelines, and set up new logic. In some cases, it’s easier to start from scratch than to “fix” the old system.'
    },
    {
      id: '5',
      question: 'What integrations are included in the implementation package?',
      answer: 'We connect everything your business needs: website, telephony, messengers, email, mailing services, warehouse and accounting systems, marketing tools. If you need something unique, we can develop a custom solution.'
    },
    {
      id: '6',
      question: 'Will there be process automation?',
      answer: 'Yes. We set up automated tasks, notifications, mailings, stage changes, document generation, ad launches, and more—customized for your processes.'
    },
    {
      id: '7',
      question: 'How is the team trained?',
      answer: 'We conduct interactive online sessions (Zoom/Google Meet), answer questions, and provide video guides and materials for self-study. For distributed teams, we provide recordings and documentation.'
    },
    {
      id: '8',
      question: 'What if something breaks after implementation?',
      answer: 'You get 30 days of free support after launch. After that, we can provide ongoing support on a subscription basis or as needed. We resolve issues promptly.'
    },
    {
      id: '9',
      question: 'How much does Kommo CRM implementation cost?',
      answer: 'The price depends on the scope, number of channels, and logic complexity. We tailor the solution to your needs and budget—after a free audit.'
    },
    {
      id: '10',
      question: 'Can you connect our website, email, and messengers?',
      answer: 'Yes. All communications are unified in Kommo. You no longer need to search through different platforms—everything is in one place.'
    },
    {
      id: '11',
      question: 'How is our data protected?',
      answer: 'We use only official integrations and certified solutions. All data is stored on secure Kommo servers, with access managed by roles and permissions.'
    },
    {
      id: '12',
      question: 'Can Kommo be customized for our specific needs?',
      answer: 'Yes. We develop custom integrations, widgets, and automations for any business process. Non-standard requests are welcome.'
    },
    {
      id: '13',
      question: 'How do we measure the effectiveness of implementation?',
      answer: 'We set up analytics to track conversion, lead processing speed, manager efficiency, and ROI. We provide reports and recommendations for further optimization.'
    },
    {
      id: '14',
      question: 'What experience does your team have?',
      answer: '10+ years of CRM implementation, 320+ successful projects, 4,000+ managers trained, 30+ integrations developed. We work with B2B, real estate, SaaS, services, and other sectors.'
    },
    {
      id: '15',
      question: 'What bonuses do I get when I apply?',
      answer: 'Free audit, individual implementation plan, ROI calculator, automation checklist, expert consultation, and examples of successful cases.'
    }
  ];

  return (
    <div className="min-h-screen">
      <PricingSchema />
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                Kommo CRM Implementation
              </h1>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                We configure the system to work for your business: automating sales, ensuring process transparency,
                and integrating with essential services. You get more than just a CRM you gain a <span className="font-semibold text-white">tool for growth</span>.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="primary"
                  size="lg"
                  className="shadow-xl px-8 py-4"
                  shakeDelay={10000}
                >
                  Start Implementation
                </AnimatedCTA>
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="bg-white bg-opacity-20 text-white border-white hover:bg-white hover:text-blue-700 px-8 py-4"
                  shakeDelay={15000}
                >
                  Get Free Audit
                </AnimatedCTA>
              </div>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">8-10 days implementation</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">Full team training included</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">30 days free support</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">All integrations included</span>
                </div>
              </div>
            </FadeInSection>

            {/* Right Visual - Interactive CRM Dashboard */}
            <FadeInSection delay={200} className="relative">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-8 border border-white border-opacity-20">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    Kommo Implementation Dashboard
                  </h3>
                  <p className="text-sm text-blue-200">Project Progress Overview</p>
                </div>

                {/* Progress Steps */}
                <div className="flex justify-between items-center mb-8">
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      1
                    </div>
                    <span className="text-xs text-blue-200 text-center">Audit</span>
                  </div>
                  <div className="flex-1 h-0.5 bg-blue-400 mx-2"></div>
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      2
                    </div>
                    <span className="text-xs text-blue-200 text-center">Setup</span>
                  </div>
                  <div className="flex-1 h-0.5 bg-blue-400 mx-2"></div>
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      3
                    </div>
                    <span className="text-xs text-blue-200 text-center">Training</span>
                  </div>
                </div>

                {/* Status Cards */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-blue-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">System Status</div>
                    <div className="text-xs text-blue-200">Active</div>
                  </div>
                  <div className="bg-purple-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Integrations</div>
                    <div className="text-xs text-blue-200">Connected</div>
                  </div>
                  <div className="bg-green-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Team Training</div>
                    <div className="text-xs text-blue-200">Completed</div>
                  </div>
                  <div className="bg-orange-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Support</div>
                    <div className="text-xs text-blue-200">Available</div>
                  </div>
                </div>

                <div className="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                  <p className="text-white text-sm font-medium">
                    🚀 Ready to transform your sales process?
                  </p>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Who This Service Is For */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Who This Service Is For
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                If you recognize yourself in at least one situation - implementation will bring noticeable results
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <FadeInSection delay={200}>
              <Card variant="outlined" className="p-8 text-center h-full hover:shadow-lg transition-shadow">
                <div className="text-red-500 text-5xl mb-6">⚠️</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Starting Fresh
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  You&apos;re just transitioning to Kommo and want to do everything right from the first time.
                </p>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="outlined" className="p-8 text-center h-full hover:shadow-lg transition-shadow">
                <div className="text-red-500 text-5xl mb-6">😤</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Team Resistance
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  You already have a CRM, but managers sabotage it, and you don&apos;t see real numbers.
                </p>
              </Card>
            </FadeInSection>

            <FadeInSection delay={600}>
              <Card variant="outlined" className="p-8 text-center h-full hover:shadow-lg transition-shadow">
                <div className="text-red-500 text-5xl mb-6">😵‍💫</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  Lost in Complexity
                </h3>
                <p className="text-gray-700 leading-relaxed">
                  You want to control sales, but get lost in reports and tasks.
                </p>
              </Card>
            </FadeInSection>
          </div>
        </div>
      </section>


      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                How the Project Works
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Clear process with defined stages and deliverables
              </p>
            </div>
          </FadeInSection>

          {/* Modern Vertical Timeline */}
          <div className="max-w-4xl mx-auto">
            <div className="relative">
              {/* Central vertical line - hidden on mobile */}
              <div className="hidden md:block absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-500 via-green-500 via-purple-500 to-orange-500 rounded-full"></div>

              {/* Mobile vertical line */}
              <div className="md:hidden absolute left-8 top-0 w-1 h-full bg-gradient-to-b from-blue-500 via-green-500 via-purple-500 to-orange-500 rounded-full"></div>

              <div className="space-y-16">
                {/* Step 1 */}
                <FadeInSection delay={200}>
                  <div className="relative flex items-center">
                    {/* Desktop layout */}
                    <div className="hidden md:flex flex-1 pr-8 text-right">
                      <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">Audit of the sales department</h3>
                        <p className="text-gray-600 leading-relaxed">
                          We will thoroughly analyze the business processes in the sales department and on the basis of this information we will develop a CRM implementation concept.
                        </p>
                      </div>
                    </div>

                    {/* Circle number */}
                    <div className="relative z-10 w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-700 text-white rounded-full flex items-center justify-center text-xl font-bold shadow-lg md:mx-0 mx-0">
                      1
                    </div>

                    {/* Mobile layout */}
                    <div className="md:hidden flex-1 pl-8">
                      <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-blue-500 hover:shadow-xl transition-shadow">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">Audit of the sales department</h3>
                        <p className="text-gray-600 leading-relaxed">
                          We will thoroughly analyze the business processes in the sales department and on the basis of this information we will develop a CRM implementation concept.
                        </p>
                      </div>
                    </div>

                    {/* Desktop empty space */}
                    <div className="hidden md:block flex-1 pl-8"></div>
                  </div>
                </FadeInSection>

                {/* Step 2 */}
                <FadeInSection delay={400}>
                  <div className="relative flex items-center">
                    {/* Desktop empty space */}
                    <div className="hidden md:block flex-1 pr-8"></div>

                    {/* Circle number */}
                    <div className="relative z-10 w-16 h-16 bg-gradient-to-br from-green-500 to-green-700 text-white rounded-full flex items-center justify-center text-xl font-bold shadow-lg">
                      2
                    </div>

                    {/* Content for both mobile and desktop */}
                    <div className="flex-1 pl-8">
                      <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 md:border-l-0 md:border-r-4 border-green-500 hover:shadow-xl transition-shadow">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">System Setup</h3>
                        <p className="text-gray-600 leading-relaxed">
                          Based on the company&apos;s business processes, we will develop and configure sales funnels, additional fields, automate business processes, and integrate with the website, telephony, messengers, and other services.
                        </p>
                      </div>
                    </div>
                  </div>
                </FadeInSection>

                {/* Step 3 */}
                <FadeInSection delay={600}>
                  <div className="relative flex items-center">
                    {/* Desktop layout */}
                    <div className="hidden md:flex flex-1 pr-8 text-right">
                      <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">Training</h3>
                        <p className="text-gray-600 leading-relaxed">
                          Our specialist will provide interactive Zoom / Google Meet training, answer all questions and provide additional materials for further use and onboarding.
                        </p>
                      </div>
                    </div>

                    {/* Circle number */}
                    <div className="relative z-10 w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-700 text-white rounded-full flex items-center justify-center text-xl font-bold shadow-lg">
                      3
                    </div>

                    {/* Mobile layout */}
                    <div className="md:hidden flex-1 pl-8">
                      <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 border-purple-500 hover:shadow-xl transition-shadow">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">Training</h3>
                        <p className="text-gray-600 leading-relaxed">
                          Our specialist will provide interactive Zoom / Google Meet training, answer all questions and provide additional materials for further use and onboarding.
                        </p>
                      </div>
                    </div>

                    {/* Desktop empty space */}
                    <div className="hidden md:block flex-1 pl-8"></div>
                  </div>
                </FadeInSection>

                {/* Step 4 */}
                <FadeInSection delay={800}>
                  <div className="relative flex items-center">
                    {/* Desktop empty space */}
                    <div className="hidden md:block flex-1 pr-8"></div>

                    {/* Circle number */}
                    <div className="relative z-10 w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-700 text-white rounded-full flex items-center justify-center text-xl font-bold shadow-lg">
                      4
                    </div>

                    {/* Content for both mobile and desktop */}
                    <div className="flex-1 pl-8">
                      <div className="bg-white rounded-xl p-6 shadow-lg border-l-4 md:border-l-0 md:border-r-4 border-orange-500 hover:shadow-xl transition-shadow">
                        <h3 className="text-xl font-bold text-gray-900 mb-3">Provide support and create new growth points</h3>
                        <p className="text-gray-600 leading-relaxed">
                          We assist and advise after implementation and contribute to the further development of the system and automation.
                        </p>
                      </div>
                    </div>
                  </div>
                </FadeInSection>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trust Markers */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8">
                Trusted by
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="text-blue-600 text-3xl mb-3">🏆</div>
                  <h3 className="font-semibold text-gray-900 mb-2">Official Kommo CRM Partner</h3>
                  <p className="text-gray-600 text-sm">Certified expertise</p>
                </div>
                <div className="text-center">
                  <div className="text-green-600 text-3xl mb-3">📈</div>
                  <h3 className="font-semibold text-gray-900 mb-2">320+ successful implementations</h3>
                  <p className="text-gray-600 text-sm">Proven track record</p>
                </div>
                <div className="text-center">
                  <div className="text-purple-600 text-3xl mb-3">🏢</div>
                  <h3 className="font-semibold text-gray-900 mb-2">B2B, real estate, SaaS</h3>
                  <p className="text-gray-600 text-sm">Multi-industry experience</p>
                </div>
                <div className="text-center">
                  <div className="text-orange-600 text-3xl mb-3">🛡️</div>
                  <h3 className="font-semibold text-gray-900 mb-2">Guaranteed results</h3>
                  <p className="text-gray-600 text-sm">Post-launch support</p>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Mid-page CTA */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <FadeInSection>
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Want to see how it works for your business?
            </h2>
            <AnimatedCTA
              variant="secondary"
              size="lg"
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4"
              shakeDelay={8000}
            >
              Book a demo
            </AnimatedCTA>
          </FadeInSection>
        </div>
      </section>

      {/* Implementation Plans */}
      <PricingPlans />


      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What You Get
              </h2>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FadeInSection delay={200}>
              <FeatureCard
                icon="👁️"
                title="Transparent sales pipeline"
                description="Track every client step"
                variant="elevated"
              />
            </FadeInSection>

            <FadeInSection delay={400}>
              <FeatureCard
                icon="⚡"
                title="Routine automation"
                description="Less manual work, more time for selling"
                variant="elevated"
              />
            </FadeInSection>

            <FadeInSection delay={600}>
              <FeatureCard
                icon="📈"
                title="Increased conversion"
                description="No leads slip through the cracks"
                variant="elevated"
              />
            </FadeInSection>

            <FadeInSection delay={800}>
              <FeatureCard
                icon="📊"
                title="Managed sales department"
                description="All KPIs and reports at your fingertips"
                variant="elevated"
              />
            </FadeInSection>
          </div>

          <FadeInSection delay={1000} className="mt-16">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg p-6 shadow-lg">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Benefits:</h3>
                <ul className="space-y-2 text-gray-700">
                  <li className="flex items-center">
                    <span className="text-green-500 mr-3">✓</span>
                    Reduced lead loss through automation
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-500 mr-3">✓</span>
                    Manager time savings (up to 10 hours per week)
                  </li>
                  <li className="flex items-center">
                    <span className="text-green-500 mr-3">✓</span>
                    Fast launch: from 7 to 30 days
                  </li>
                </ul>
              </div>
              <div className="bg-blue-50 rounded-lg p-6 border-l-4 border-blue-500">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Want to see how this could work for you?</h3>
                <p className="text-gray-700">
                  Get a free audit and a personalized results forecast.
                </p>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Certified Partner Section */}
      <CertifiedPartnerSection variant="blue" />

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Frequently Asked Questions
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Everything you need to know about Kommo CRM implementation
              </p>
            </div>
          </FadeInSection>

          <div className="mt-16">
            <AccordionFAQ items={implementationFAQ} variant="bordered" />
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <FadeInSection>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Calculate Implementation Cost for Your Company
                </h2>
                <p className="text-xl text-gray-300 mb-8">
                  Fill out the form and get a personalized quote within 24 hours
                </p>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <span className="text-green-400 text-xl mr-3">✓</span>
                    <span>Free consultation and audit</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 text-xl mr-3">✓</span>
                    <span>Detailed implementation plan</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 text-xl mr-3">✓</span>
                    <span>ROI calculator as a bonus</span>
                  </div>
                </div>
              </div>
            </FadeInSection>

            <FadeInSection delay={200}>
              <div>
                <ContactForm
                  title="Get Implementation Quote"
                  subtitle=""
                  showGift={true}
                  giftText="✅ Yes, I want to receive the free 'CRM Implementation ROI Calculator'"
                  buttonText="Send Request"
                  className="bg-white text-gray-900"
                />
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>
    </div>
  );
}
