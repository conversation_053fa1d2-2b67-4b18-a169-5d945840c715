import React from 'react';
import { Metadata } from 'next';
import Card from '@/components/Card';
import ContactForm from '@/components/ContactForm';

import FeatureCard from '@/components/FeatureCard';


import FadeInSection from '@/components/FadeInSection';
import AnimatedCTA from '@/components/AnimatedCTA';
import CertifiedPartnerSection from '@/components/CertifiedPartnerSection';
import PricingPlans from '@/components/PricingPlans';
import PricingSchema from '@/components/PricingSchema';
import EnhancedFAQ from '@/components/EnhancedFAQ';
import FAQSchema from '@/components/FAQSchema';
import AutomationScenarios from '@/components/AutomationScenarios';
import PersonalizedAnalysis from '@/components/PersonalizedAnalysis';
import DetailedImplementationSteps from '@/components/DetailedImplementationSteps';
import WhoThisServiceIsFor from '@/components/WhoThisServiceIsFor';

export const metadata: Metadata = {
  title: 'Kommo CRM Implementation Pricing | $1,200-$6,000+ | Setmee - Certified Partner',
  description: 'Transparent Kommo CRM implementation pricing: Basic ($1,200), Standard ($3,000), Enterprise ($6,000+). Professional setup, automation, and training from certified partners.',
  keywords: 'Kommo CRM implementation, CRM setup pricing, CRM automation for business, Kommo partner, sales automation, CRM implementation cost',
  openGraph: {
    title: 'Kommo CRM Implementation Pricing | Transparent Plans | Setmee',
    description: 'Professional Kommo CRM implementation with transparent pricing. Choose from Basic, Standard, or Enterprise plans. Free audit included.',
    type: 'website',
  },
};

export default function ImplementationPage() {




  // FAQ data for implementation
  const implementationFAQ = [
    {
      id: '1',
      question: 'How long does it take to implement Kommo CRM?',
      answer: 'On average, it takes 7 to 30 days, depending on the complexity of your business processes, number of channels, and required integrations. Simple projects can be completed within a week; complex ones may take up to a month.'
    },
    {
      id: '2',
      question: 'When will we see the first results?',
      answer: 'Usually, you’ll notice improvements within the first week after launch: automation starts working, leads are no longer lost, and managers have clear tasks and deal stages. Within a month, you’ll see increased conversion and transparency in your sales department.'
    },
    {
      id: '3',
      question: 'What do you need from our team?',
      answer: 'At the start, we require the involvement of a responsible manager for interviews and structure approval. After that, we handle everything: setup, integrations, training, and support.'
    },
    {
      id: '4',
      question: 'Can you implement Kommo if we already have a CRM?',
      answer: 'Yes. We frequently work with companies that already have a CRM. We conduct an audit, identify issues, optimize pipelines, and set up new logic. In some cases, it’s easier to start from scratch than to “fix” the old system.'
    },
    {
      id: '5',
      question: 'What integrations are included in the implementation package?',
      answer: 'We connect everything your business needs: website, telephony, messengers, email, mailing services, warehouse and accounting systems, marketing tools. If you need something unique, we can develop a custom solution.'
    },
    {
      id: '6',
      question: 'Will there be process automation?',
      answer: 'Yes. We set up automated tasks, notifications, mailings, stage changes, document generation, ad launches, and more—customized for your processes.'
    },
    {
      id: '7',
      question: 'How is the team trained?',
      answer: 'We conduct interactive online sessions (Zoom/Google Meet), answer questions, and provide video guides and materials for self-study. For distributed teams, we provide recordings and documentation.'
    },
    {
      id: '8',
      question: 'What if something breaks after implementation?',
      answer: 'You get 30 days of free support after launch. After that, we can provide ongoing support on a subscription basis or as needed. We resolve issues promptly.'
    },
    {
      id: '10',
      question: 'Can you connect our website, email, and messengers?',
      answer: 'Yes. All communications are unified in Kommo. You no longer need to search through different platforms—everything is in one place.'
    },
    {
      id: '11',
      question: 'How is our data protected?',
      answer: 'We use only official integrations and certified solutions. All data is stored on secure Kommo servers, with access managed by roles and permissions.'
    },
    {
      id: '12',
      question: 'Can Kommo be customized for our specific needs?',
      answer: 'Yes. We develop custom integrations, widgets, and automations for any business process. Non-standard requests are welcome.'
    },
    {
      id: '13',
      question: 'How do we measure the effectiveness of implementation?',
      answer: 'We set up analytics to track conversion, lead processing speed, manager efficiency, and ROI. We provide reports and recommendations for further optimization.'
    },
    {
      id: '14',
      question: 'What experience does your team have?',
      answer: '10+ years of CRM implementation, 320+ successful projects, 4,000+ managers trained, 30+ integrations developed. We work with B2B, real estate, SaaS, services, and other sectors.'
    },
    {
      id: '15',
      question: 'What bonuses do I get when I apply?',
      answer: 'Free audit, individual implementation plan, ROI calculator, automation checklist, expert consultation, and examples of successful cases.'
    }
  ];

  return (
    <div className="min-h-screen">
      <PricingSchema />
      <FAQSchema />
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                Kommo CRM Implementation
              </h1>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                We configure the system to work for your business: automating sales, ensuring process transparency,
                and integrating with essential services. You get more than just a CRM you gain a <span className="font-semibold text-white">tool for growth</span>.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="primary"
                  size="lg"
                  className="shadow-xl px-8 py-4"
                  shakeDelay={10000}
                >
                  Start Implementation
                </AnimatedCTA>
                <AnimatedCTA
                  variant="secondary"
                  size="lg"
                  className="bg-white bg-opacity-20 text-white border-white hover:bg-white hover:text-blue-700 px-8 py-4"
                  shakeDelay={15000}
                >
                  Get Free Audit
                </AnimatedCTA>
              </div>

              {/* Key Benefits */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-8">
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">8-10 days implementation</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">Full team training included</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">30 days free support</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-400 text-xl mr-3">✓</span>
                  <span className="text-blue-100">All integrations included</span>
                </div>
              </div>
            </FadeInSection>

            {/* Right Visual - Interactive CRM Dashboard */}
            <FadeInSection delay={200} className="relative">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-8 border border-white border-opacity-20">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    Kommo Implementation Dashboard
                  </h3>
                  <p className="text-sm text-blue-200">Project Progress Overview</p>
                </div>

                {/* Progress Steps */}
                <div className="flex justify-between items-center mb-8">
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      1
                    </div>
                    <span className="text-xs text-blue-200 text-center">Audit</span>
                  </div>
                  <div className="flex-1 h-0.5 bg-blue-400 mx-2"></div>
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      2
                    </div>
                    <span className="text-xs text-blue-200 text-center">Setup</span>
                  </div>
                  <div className="flex-1 h-0.5 bg-blue-400 mx-2"></div>
                  <div className="flex flex-col items-center">
                    <div className="w-10 h-10 bg-yellow-500 text-white rounded-full flex items-center justify-center text-sm font-semibold mb-2">
                      3
                    </div>
                    <span className="text-xs text-blue-200 text-center">Training</span>
                  </div>
                </div>

                {/* Status Cards */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-blue-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">System Status</div>
                    <div className="text-xs text-blue-200">Active</div>
                  </div>
                  <div className="bg-purple-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Integrations</div>
                    <div className="text-xs text-blue-200">Connected</div>
                  </div>
                  <div className="bg-green-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Team Training</div>
                    <div className="text-xs text-blue-200">Completed</div>
                  </div>
                  <div className="bg-orange-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Support</div>
                    <div className="text-xs text-blue-200">Available</div>
                  </div>
                </div>

                <div className="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                  <p className="text-white text-sm font-medium">
                    🚀 Ready to transform your sales process?
                  </p>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Who This Service Is For */}
      <WhoThisServiceIsFor />


      {/* Detailed Implementation Steps */}
      <DetailedImplementationSteps />

      {/* Trust Markers */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-8">
                Trusted by
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="text-blue-600 text-3xl mb-3">🏆</div>
                  <h3 className="font-semibold text-gray-900 mb-2">Official Kommo CRM Partner</h3>
                  <p className="text-gray-600 text-sm">Certified expertise</p>
                </div>
                <div className="text-center">
                  <div className="text-green-600 text-3xl mb-3">📈</div>
                  <h3 className="font-semibold text-gray-900 mb-2">320+ successful implementations</h3>
                  <p className="text-gray-600 text-sm">Proven track record</p>
                </div>
                <div className="text-center">
                  <div className="text-purple-600 text-3xl mb-3">🏢</div>
                  <h3 className="font-semibold text-gray-900 mb-2">B2B, real estate, SaaS</h3>
                  <p className="text-gray-600 text-sm">Multi-industry experience</p>
                </div>
                <div className="text-center">
                  <div className="text-orange-600 text-3xl mb-3">🛡️</div>
                  <h3 className="font-semibold text-gray-900 mb-2">Guaranteed results</h3>
                  <p className="text-gray-600 text-sm">Post-launch support</p>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Mid-page CTA */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <FadeInSection>
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
              Want to see how it works for your business?
            </h2>
            <AnimatedCTA
              variant="secondary"
              size="lg"
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4"
              shakeDelay={8000}
            >
              Book a demo
            </AnimatedCTA>
          </FadeInSection>
        </div>
      </section>

      {/* Implementation Plans */}
      <PricingPlans />


      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <FadeInSection>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                What You Get with Kommo Implementation
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                Measurable results that transform your sales process and drive business growth
              </p>
            </div>
          </FadeInSection>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <FadeInSection delay={200}>
              <Card variant="elevated" className="p-6 text-center hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Transparent Pipeline</h3>
                <p className="text-gray-600">Every deal and client step is tracked — no lost leads.</p>
              </Card>
            </FadeInSection>

            <FadeInSection delay={400}>
              <Card variant="elevated" className="p-6 text-center hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Routine Automation</h3>
                <p className="text-gray-600">Managers save up to 10 hours per week through automated tasks and reminders.</p>
              </Card>
            </FadeInSection>

            <FadeInSection delay={600}>
              <Card variant="elevated" className="p-6 text-center hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Conversion Growth</h3>
                <p className="text-gray-600">Typical clients see 20–30% more closed deals within 3 months.</p>
              </Card>
            </FadeInSection>

            <FadeInSection delay={800}>
              <Card variant="elevated" className="p-6 text-center hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">KPI Dashboard</h3>
                <p className="text-gray-600">All sales metrics and reports in one place for instant decision-making.</p>
              </Card>
            </FadeInSection>

            <FadeInSection delay={1000}>
              <Card variant="elevated" className="p-6 text-center hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Fast Launch</h3>
                <p className="text-gray-600">Go live in 7–30 days, with onboarding and full support included.</p>
              </Card>
            </FadeInSection>

            <FadeInSection delay={1200}>
              <Card variant="elevated" className="p-6 text-center hover:shadow-xl transition-all duration-300">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">Guaranteed Support</h3>
                <p className="text-gray-600">30 days post-launch support, with options for ongoing assistance.</p>
              </Card>
            </FadeInSection>
          </div>

          {/* Proven Impact */}
          <FadeInSection delay={1400}>
            <div className="bg-gray-50 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Proven Impact</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div>
                  <div className="text-3xl font-bold text-blue-600 mb-2">2–4 months</div>
                  <p className="text-gray-600">Average payback period</p>
                </div>
                <div>
                  <div className="text-3xl font-bold text-green-600 mb-2">100%</div>
                  <p className="text-gray-600">Of leads captured in CRM</p>
                </div>
                <div>
                  <div className="text-3xl font-bold text-purple-600 mb-2">320+</div>
                  <p className="text-gray-600">Successful implementations</p>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Automation Scenarios */}
      <AutomationScenarios />

      {/* Personalized Analysis CTA */}
      <PersonalizedAnalysis />

      {/* Certified Partner Section */}
      <CertifiedPartnerSection variant="blue" />

      {/* Enhanced FAQ Section */}
      <EnhancedFAQ />

      {/* Final CTA */}
      <section className="py-20 bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <FadeInSection>
              <div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6">
                  Calculate Implementation Cost for Your Company
                </h2>
                <p className="text-xl text-gray-300 mb-8">
                  Fill out the form and get a personalized quote within 24 hours
                </p>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <span className="text-green-400 text-xl mr-3">✓</span>
                    <span>Free consultation and audit</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 text-xl mr-3">✓</span>
                    <span>Detailed implementation plan</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-green-400 text-xl mr-3">✓</span>
                    <span>ROI calculator as a bonus</span>
                  </div>
                </div>
              </div>
            </FadeInSection>

            <FadeInSection delay={200}>
              <div>
                <ContactForm
                  title="Get Implementation Quote"
                  subtitle=""
                  showGift={true}
                  giftText="✅ Yes, I want to receive the free 'CRM Implementation ROI Calculator'"
                  buttonText="Send Request"
                  className="bg-white text-gray-900"
                />
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>
    </div>
  );
}
