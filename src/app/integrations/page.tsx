import React from 'react';
import { Metadata } from 'next';

import ContactForm from '@/components/ContactForm';
import SectionHeading from '@/components/SectionHeading';
import FeatureCard from '@/components/FeatureCard';
import Timeline from '@/components/Timeline';
import AccordionFAQ from '@/components/AccordionFAQ';
import FadeInSection from '@/components/FadeInSection';
import AnimatedCT<PERSON> from '@/components/AnimatedCTA';
import WaveDivider from '@/components/WaveDivider';
import MetricCard from '@/components/MetricCard';
import IntegrationDiagram from '@/components/IntegrationDiagram';
import { trustMarkers } from '@/data/content';

export const metadata: Metadata = {
  title: 'Kommo CRM Integrations via Make.com | Automate Any Process | Setmee',
  description: 'Connect Kommo CRM with any services using Make.com. Automate routine tasks, sync data, create custom workflows without programmers.',
  keywords: 'Kommo integrations, Make.com, CRM automation, workflow automation, API integrations, Kommo Make',
  openGraph: {
    title: 'Kommo CRM Integrations via Make.com | Setmee',
    description: 'Connect Kommo CRM with any services using Make.com. Automate routine tasks without programmers.',
    type: 'website',
  },
};

export default function IntegrationsPage() {
  // Timeline data for integration process
  const integrationTimeline = [
    {
      id: '1',
      title: 'Needs and Architecture Analysis',
      description: 'Study your current processes and necessary integrations. Design of scenario logic on the Make platform.',
      duration: '1-3 days',
      icon: '🔍',
      details: [
        'Current workflow analysis',
        'Integration requirements gathering',
        'Creation of scenarios on the Make platform',
        'Data flow mapping',
        'Security and compliance review'
      ]
    },
    {
      id: '2',
      title: 'Integration Development',
      description: 'Create and configure scenarios on the Make platform, set up connections between services.',
      duration: '3-7 days',
      icon: '⚙️',
      details: [
        'Creation of scenarios on the Make platform',
        'API connections setup',
        'Data mapping configuration',
        'Error handling implementation',
        'Performance optimization'
      ]
    },
    {
      id: '3',
      title: 'Testing and Launch',
      description: 'Test all scenarios, fix issues, and launch integrations with monitoring.',
      duration: '1-2 days',
      icon: '🚀',
      details: [
        'Comprehensive scenario testing',
        'Edge case validation',
        'Performance testing',
        'Production deployment',
        'Monitoring setup'
      ]
    }
  ];

  // FAQ data for integrations
  const integrationFAQ = [
    {
      id: '1',
      question: 'What is Make.com and why do you use it for integrations?',
      answer: 'The Make platform (formerly Integromat) is a powerful automation tool that enables connecting different services without programming. We use it because it\'s reliable, cost-effective, and provides a visual workflow builder that\'s easy to understand and modify.'
    },
    {
      id: '3',
      question: 'Can you integrate Kommo with any service?',
      answer: 'We can integrate Kommo with any service that has an API or is supported by Make.com. This includes 1000+ popular services like Google Workspace, Microsoft 365, Slack, Telegram, WhatsApp, payment systems, and many more.'
    },
    {
      id: '4',
      question: 'What happens if an integration breaks?',
      answer: 'We provide monitoring and support for all integrations we create. If something breaks, we\'ll fix it quickly. We also provide documentation and training so your team can handle simple issues independently.'
    },
    {
      id: '5',
      question: 'How long does it take to set up integrations?',
      answer: 'Simple integrations can be ready in 3-7 days, while complex multi-service workflows typically take 1-2 weeks. We provide a detailed timeline after analyzing your requirements.'
    },
    {
      id: '10',
      question: 'What services can be integrated with Kommo CRM?',
      answer: 'We can connect Kommo with a wide range of external services, websites, and apps if there’s an API or integration option, we can automate it.'
    },
    {
      id: '11',
      question: 'Will you need access to my accounts?',
      answer: 'Yes, secure access is required to set up and test integrations. All credentials are handled confidentially.'
    },
    {
      id: '13',
      question: 'Can workflows be changed later?',
      answer: 'Yes, scenarios can be updated or expanded as your processes evolve.'
    },
    {
      id: '14',
      question: 'I don’t have a detailed technical brief. Is that a problem?',
      answer: 'No. We help clarify your needs and design the optimal integration based on your business goals.'
    },
    {
      id: '15',
      question: 'What does integration setup cost?',
      answer: 'Pricing is transparent and based on the number and complexity of integrations. Contact us for a tailored quote.'
    },
    {
      id: '16',
      question: 'Do I need a paid Make.com subscription?',
      answer: 'Some scenarios can be implemented with a free plan, but advanced or high-volume automations may require a paid Make platform subscription.'
    },
    {
      id: '17',
      question: 'What if something breaks?',
      answer: 'Every automation scenario includes:\n • Built-in error notifications\n • Step-by-step logs (what happened and when)\n • The ability to roll back or quickly edit workflows\n\nAdditionally, we offer ongoing monitoring and support by agreement.'
    },
    {
      id: '18',
      question: 'We already have a partial integration. Can you improve or finish it?',
      answer: 'Yes, we can audit, refine, or complete existing workflows—even if they were started by another provider.'
    }
  ];

  // Popular integration examples
  const integrationExamples = [
    {
      category: 'Google Services',
      services: ['Google Sheets', 'Google Docs', 'Google Drive', 'Google Calendar', 'Google Analytics'],
      icon: '📊',
      description: 'Sync data, create documents, schedule meetings automatically'
    },
    {
      category: 'Communication',
      services: ['Telegram', 'WhatsApp', 'Slack', 'Email', 'SMS'],
      icon: '💬',
      description: 'Automated notifications, customer communication, team alerts'
    },
    {
      category: 'Productivity Tools',
      services: ['Notion', 'Airtable', 'ClickUp', 'Trello', 'Asana'],
      icon: '⚡',
      description: 'Project management, task creation, data synchronization'
    },
    {
      category: 'E-commerce & Payments',
      services: ['Stripe', 'PayPal', 'WooCommerce', 'Shopify'],
      icon: '💳',
      description: 'Payment processing, order management, invoice generation'
    },
    {
      category: 'Marketing & Analytics',
      services: ['Facebook Ads', 'Google Ads', 'Mailchimp'],
      icon: '📈',
      description: 'Lead tracking, campaign optimization, analytics reporting'
    },
    {
      category: 'File Storage & Documents',
      services: ['Dropbox', 'OneDrive', 'Box', 'PDF generation', 'DocuSign'],
      icon: '📁',
      description: 'Document automation, file synchronization, digital signatures'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-600 via-purple-700 to-indigo-800 py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <FadeInSection className="text-white">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                <span className="text-purple-300">Kommo CRM</span> Integrations via <span className="text-yellow-300">Make</span>: Automate Any Process Without Programmers
              </h1>
              <p className="text-xl text-purple-100 mb-8 leading-relaxed">
                Connect your Kommo CRM with any external services, websites, and applications using Make.com.
                Fully automate routine tasks, freeing up your team&apos;s time for what matters most - <span className="font-semibold text-white">sales</span>.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-start items-center mb-8">
                <AnimatedCTA
                  variant="primary"
                  size="lg"
                  className="shadow-xl px-8 py-4"
                  shakeDelay={10000}
                >
                  Get Custom Integration
                </AnimatedCTA>
                <div className="flex items-center text-purple-200 text-sm">
                  <span className="mr-2">⚡</span>
                  <span>Ready in 1-2 weeks</span>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center text-purple-200">
                  <span className="mr-2">🤖</span>
                  <span>Full Automation</span>
                </div>
                <div className="flex items-center text-purple-200">
                  <span className="mr-2">🔧</span>
                  <span>No Programming</span>
                </div>
                <div className="flex items-center text-purple-200">
                  <span className="mr-2">🚀</span>
                  <span>1000+ Services</span>
                </div>
              </div>
            </FadeInSection>

            {/* Right Visual - Make.com Integration Dashboard */}
            <FadeInSection delay={200} className="relative">
              <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-8 border border-white border-opacity-20">
                <div className="text-center mb-6">
                  <h3 className="text-lg font-semibold text-white mb-2">
                    Make.com Integration Hub
                  </h3>
                  <p className="text-sm text-purple-200">Kommo CRM Connections</p>
                </div>

                {/* Integration Flow Visualization */}
                <div className="flex justify-center items-center mb-8">
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mb-2 p-3">
                      <img
                        src="/Make App White Logo.png"
                        alt="Make.com"
                        className="w-full h-full object-contain"
                      />
                    </div>
                    <span className="text-xs text-purple-200 text-center font-medium">Make.com</span>
                  </div>
                  <div className="flex-1 max-w-[120px] mx-6">
                    <div className="relative">
                      <div className="h-0.5 bg-gradient-to-r from-purple-400 to-indigo-400 w-full"></div>
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                      </div>
                      <div className="absolute top-0 right-0 transform translate-x-1 -translate-y-1/2">
                        <div className="w-0 h-0 border-l-2 border-r-2 border-b-2 border-transparent border-b-indigo-400"></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col items-center">
                    <div className="w-16 h-16 bg-indigo-600 rounded-full flex items-center justify-center mb-2">
                      <span className="text-white text-2xl">💼</span>
                    </div>
                    <span className="text-xs text-purple-200 text-center font-medium">Kommo CRM</span>
                  </div>
                </div>

                {/* Connected Services */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-indigo-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Google Services</div>
                    <div className="text-xs text-purple-200">Connected ✓</div>
                  </div>
                  <div className="bg-blue-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Messaging Apps</div>
                    <div className="text-xs text-purple-200">Connected ✓</div>
                  </div>
                  <div className="bg-green-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Payment Systems</div>
                    <div className="text-xs text-purple-200">Connected ✓</div>
                  </div>
                  <div className="bg-yellow-50 bg-opacity-20 p-4 rounded-lg">
                    <div className="text-sm font-semibold text-white mb-1">Marketing Tools</div>
                    <div className="text-xs text-purple-200">Connected ✓</div>
                  </div>
                </div>

                <div className="bg-white bg-opacity-20 rounded-lg p-4 text-center">
                  <p className="text-white text-sm font-medium">
                    🔗 Connect with 1000+ services without coding
                  </p>
                </div>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      <WaveDivider color="white" />

      {/* Integration Diagram */}
      <section className="py-20 bg-white">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Visual Overview"
            title="How Integrations Work"
            subtitle="Connect Kommo CRM with all your business tools through Make platform"
            accentWords={["Integrations", "Work"]}
            size="md"
          />

          <IntegrationDiagram animated={true} />

          <FadeInSection delay={600} className="mt-12 text-center">
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-8 border border-purple-200">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                <span className="text-purple-600">1000+</span> Services Available for Integration
              </h3>
              <p className="text-gray-600">
                From popular tools like Google Workspace and Slack to specialized industry software -
                we can connect virtually any service with your Kommo CRM.
              </p>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Integration Consultation CTA */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-indigo-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <FadeInSection className="text-white">
            <div className="inline-block px-4 py-2 mb-6 bg-white bg-opacity-20 text-white rounded-full text-sm font-semibold">
              🚀 Integration Consultation
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Ready to Connect Everything?
            </h2>
            <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
              We'll audit your current tools and design a custom integration strategy that eliminates manual work and maximizes efficiency.
            </p>
            <AnimatedCTA
              variant="primary"
              size="lg"
              className="bg-blue-600 text-white hover:bg-blue-700 shadow-xl px-10 py-4 text-lg font-semibold"
              shakeDelay={8000}
            >
              Get Custom Integration
            </AnimatedCTA>
            <div className="mt-6 flex items-center justify-center text-purple-200 text-sm">
              <span className="mr-2">⏱️</span>
              <span>30-minute consultation • Free of charge</span>
            </div>
          </FadeInSection>
        </div>
      </section>

      <WaveDivider color="gray" flip={true} />

      {/* Who This Service Is For */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Perfect For"
            title="Who This Service Is For"
            subtitle="If you're dealing with manual data transfer and disconnected systems, integrations will save you hours every day"
            accentWords={["Service"]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FadeInSection delay={0}>
              <FeatureCard
                icon="🔄"
                title="Manual Data Transfer"
                description="Your processes require constant manual data transfer between different systems."
                borderColor="purple"
                iconColor="text-purple-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={200}>
              <FeatureCard
                icon="🚫"
                title="Disconnected Systems"
                description="You use multiple services, but they don't 'communicate' with each other."
                borderColor="red"
                iconColor="text-red-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={400}>
              <FeatureCard
                icon="⚡"
                title="Automation Needs"
                description="You want to automate funnels, message sending, and document creation."
                borderColor="blue"
                iconColor="text-blue-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={600}>
              <FeatureCard
                icon="📥"
                title="Lead Collection"
                description="You need lead collection from different sources directly into Kommo."
                borderColor="green"
                iconColor="text-green-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={800}>
              <FeatureCard
                icon="💰"
                title="Cost-Effective Solutions"
                description="You're looking for flexible integration solutions without expensive developers."
                borderColor="red"
                iconColor="text-orange-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>
          </div>

          <FadeInSection delay={1000} className="mt-16">
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white text-xl">🔗</span>
                </div>
                <h3 className="text-2xl font-bold text-gray-900">
                  Why Choose <span className="text-purple-600">Make platform</span> for Integrations?
                </h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800 mb-3">Advantages:</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <span className="text-green-500 mr-3 mt-1">✅</span>
                      <span className="text-gray-700">1000+ pre-built connectors</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-green-500 mr-3 mt-1">✅</span>
                      <span className="text-gray-700">Visual workflow builder</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-green-500 mr-3 mt-1">✅</span>
                      <span className="text-gray-700">No programming required</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold text-gray-800 mb-3">Benefits:</h4>
                  <div className="space-y-3">
                    <div className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">🚀</span>
                      <span className="text-gray-700">Fast implementation (1-2 weeks)</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">💰</span>
                      <span className="text-gray-700">Cost-effective vs custom development</span>
                    </div>
                    <div className="flex items-start">
                      <span className="text-blue-500 mr-3 mt-1">🔧</span>
                      <span className="text-gray-700">Easy to modify and maintain</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Project Timeline */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Step by Step"
            title="How the Project Works"
            subtitle="From analysis to launch - a proven process for successful integrations"
            accentWords={["Project", "Works"]}
          />

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start mt-16">
            {/* Left side - Dashboard mockup */}
            <FadeInSection className="relative">
              <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8 shadow-xl border border-blue-200">
                <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                  {/* Dashboard header */}
                  <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <h3 className="text-white font-bold text-lg">Integration Dashboard</h3>
                      <div className="flex space-x-2">
                        <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                        <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                        <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Dashboard content */}
                  <div className="p-6">
                    <div className="grid grid-cols-4 gap-4 mb-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-green-600">24</div>
                        <div className="text-sm text-gray-600">Active Integrations</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-600">18</div>
                        <div className="text-sm text-gray-600">Data Sources</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-purple-600">12</div>
                        <div className="text-sm text-gray-600">Automations</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-orange-600">8</div>
                        <div className="text-sm text-gray-600">Workflows</div>
                      </div>
                    </div>
                    
                    {/* Chart mockup */}
                    <div className="bg-gray-50 rounded-lg p-4 mb-4">
                      <div className="flex items-end space-x-2 h-20">
                        <div className="bg-blue-500 w-4 h-8 rounded-t"></div>
                        <div className="bg-blue-500 w-4 h-12 rounded-t"></div>
                        <div className="bg-blue-500 w-4 h-16 rounded-t"></div>
                        <div className="bg-blue-500 w-4 h-10 rounded-t"></div>
                        <div className="bg-blue-500 w-4 h-14 rounded-t"></div>
                        <div className="bg-blue-500 w-4 h-18 rounded-t"></div>
                        <div className="bg-blue-500 w-4 h-12 rounded-t"></div>
                      </div>
                      <div className="text-xs text-gray-500 mt-2">Integration Performance - 95%</div>
                    </div>
                    
                    {/* Status indicators */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                        <span className="text-sm text-gray-700">Kommo CRM ↔ Google Workspace</span>
                        <span className="text-green-600 text-sm">✓ Active</span>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                        <span className="text-sm text-gray-700">Website Forms ↔ Kommo CRM</span>
                        <span className="text-blue-600 text-sm">⚡ Running</span>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-purple-50 rounded">
                        <span className="text-sm text-gray-700">Email Marketing ↔ Analytics</span>
                        <span className="text-purple-600 text-sm">🔄 Syncing</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </FadeInSection>

            {/* Right side - Timeline */}
            <FadeInSection delay={200}>
              <div className="space-y-8">
                {integrationTimeline.map((step, index) => (
                  <FadeInSection key={step.id} delay={300 + index * 150}>
                    <div className="flex items-start space-x-4">
                      {/* Timeline dot and duration */}
                      <div className="flex-shrink-0 flex items-center space-x-3">
                        <div className="relative">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-lg shadow-lg ${
                            index === 0 ? 'bg-blue-500' : 
                            index === 1 ? 'bg-purple-500' : 
                            'bg-green-500'
                          }`}>
                            <span className="text-xl">{step.icon}</span>
                          </div>
                          {index < integrationTimeline.length - 1 && (
                            <div className="absolute top-12 left-1/2 transform -translate-x-1/2 w-0.5 h-16 bg-gray-300"></div>
                          )}
                        </div>
                        {/* Duration badge */}
                        <div className={`px-3 py-1 rounded-full text-xs font-semibold text-white ${
                          index === 0 ? 'bg-blue-600' : 
                          index === 1 ? 'bg-purple-600' : 
                          'bg-green-600'
                        }`}>
                          {step.duration}
                        </div>
                      </div>
                      
                      {/* Content */}
                      <div className="flex-1 pb-8">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">{step.title}</h3>
                        <p className="text-gray-600 mb-4">{step.description}</p>
                        
                        {/* Details list */}
                        <ul className="space-y-2">
                          {step.details.map((detail, detailIndex) => (
                            <li key={detailIndex} className="flex items-start space-x-2 text-sm text-gray-700">
                              <span className={`w-1.5 h-1.5 rounded-full mt-2 flex-shrink-0 ${
                                index === 0 ? 'bg-blue-500' : 
                                index === 1 ? 'bg-purple-500' : 
                                'bg-green-500'
                              }`}></span>
                              <span>{detail}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </FadeInSection>
                ))}
                
                {/* Final Result block under timeline */}
                <FadeInSection delay={800} className="mt-8">
                  <div className="bg-gradient-to-r from-purple-50 to-indigo-50 border border-purple-200 rounded-xl p-6 shadow-lg">
                    <div className="flex items-center mb-4">
                      <span className="text-purple-500 text-2xl mr-3">🎯</span>
                      <h3 className="text-xl font-bold text-purple-900">Final Result</h3>
                    </div>
                    <p className="text-purple-800 font-medium">
                      Fully automated processes working like <span className="text-purple-600 font-bold">clockwork</span>, saving hours of manual work every day.
                    </p>
                  </div>
                </FadeInSection>
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>

      {/* Trust Markers */}
      <section className="py-20 bg-gradient-to-r from-blue-50 to-indigo-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Trusted Partner"
            title="Trusted with Complex Integrations"
            subtitle="We've implemented hundreds of successful integrations that bring tangible benefits to our clients"
            accentWords={["Trusted", "Integrations"]}
            size="md"
          />

          <FadeInSection>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {trustMarkers.items.map((item, index) => (
                <FadeInSection key={index} delay={index * 100}>
                  <div className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-all duration-300 text-center border border-blue-100">
                    <div className="text-blue-600 font-semibold text-lg">{item}</div>
                  </div>
                </FadeInSection>
              ))}
            </div>
          </FadeInSection>

          <FadeInSection delay={600} className="mt-12 text-center">
            <div className="bg-white rounded-xl p-8 max-w-3xl mx-auto shadow-lg border border-blue-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-purple-600 mb-2">500+</div>
                  <div className="text-gray-600">Integrations Built</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600 mb-2">1000+</div>
                  <div className="text-gray-600">Hours Saved Monthly</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600 mb-2">99.9%</div>
                  <div className="text-gray-600">Uptime Reliability</div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Mid-page CTA */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-indigo-700 relative overflow-hidden">
        <div className="absolute inset-0 bg-black opacity-10"></div>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <FadeInSection className="text-white">
            <div className="inline-block px-4 py-2 mb-6 bg-white bg-opacity-20 text-white rounded-full text-sm font-semibold">
              💡 Free Consultation
            </div>
            <h2 className="text-3xl md:text-4xl font-bold mb-6">
              Have an integration idea? <span className="text-purple-200">Let&apos;s discuss!</span>
            </h2>
            <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
              Get expert advice on your integration needs and discover automation opportunities you might have missed
            </p>
            <AnimatedCTA
              variant="primary"
              size="lg"
              className="bg-blue-600 text-white hover:bg-blue-700 shadow-xl px-10 py-4 text-lg font-semibold"
              shakeDelay={8000}
            >
              Get Integration Consultation
            </AnimatedCTA>
            <div className="mt-6 flex items-center justify-center text-purple-200 text-sm">
              <span className="mr-2">⏱️</span>
              <span>30-minute consultation • Free of charge</span>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Results Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Results"
            title="What You'll Get in the End"
            subtitle="Transform your business operations with powerful automation that works 24/7"
            accentWords={["Get", "End"]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <FadeInSection delay={0}>
              <FeatureCard
                icon="🤖"
                title="Complete Routine Automation"
                description="Your employees focus on sales, not manual data entry. Save 5-10 hours per week per employee"
                variant="elevated"
                borderColor="purple"
                iconColor="text-purple-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={200}>
              <FeatureCard
                icon="🔄"
                title="Data Synchronization"
                description="All systems 'communicate', information is always up-to-date across all platforms"
                variant="elevated"
                borderColor="blue"
                iconColor="text-blue-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={400}>
              <FeatureCard
                icon="✅"
                title="Reduced Errors"
                description="Eliminate human factor in data transfer. 99.9% accuracy in automated processes"
                variant="elevated"
                borderColor="green"
                iconColor="text-green-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={600}>
              <FeatureCard
                icon="💰"
                title="Time and Cost Savings"
                description="No need to hire programmers for each integration. ROI typically 300-500%"
                variant="elevated"
                borderColor="red"
                iconColor="text-orange-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>

            <FadeInSection delay={800}>
              <FeatureCard
                icon="📈"
                title="Flexibility and Scalability"
                description="Easy to add new integrations as business grows. Future-proof your operations"
                variant="elevated"
                borderColor="blue"
                iconColor="text-indigo-500"
                hover={true}
                size="lg"
              />
            </FadeInSection>
          </div>

          <FadeInSection delay={1000} className="text-center">
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-8 max-w-4xl mx-auto shadow-lg border border-purple-200">
              <div className="flex items-center justify-center mb-6">
                <span className="text-purple-500 text-3xl mr-3">🚀</span>
                <h3 className="text-2xl font-bold text-gray-900">Integration Benefits Summary</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <MetricCard
                  value="5-10 hours"
                  label="Weekly Time Saved"
                  description="Per employee productivity gain"
                  icon="⏰"
                  color="purple"
                  size="md"
                  delay={0}
                />
                <MetricCard
                  value="99.9%"
                  label="Process Accuracy"
                  description="Automated workflow reliability"
                  icon="✅"
                  color="green"
                  size="md"
                  delay={200}
                />
                <MetricCard
                  value="1-2 weeks"
                  label="Implementation"
                  description="Fast deployment timeline"
                  icon="🚀"
                  color="blue"
                  size="md"
                  delay={400}
                />
              </div>

              <div className="bg-white rounded-lg p-6 border-l-4 border-yellow-500">
                <p className="text-gray-800 font-medium text-lg">
                  Want to know how this can work for <span className="text-purple-600 font-bold">your business</span>?
                  Get a free consultation and custom integration roadmap.
                </p>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* Integration Examples */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="Popular Integrations"
            title="Integration Examples"
            subtitle="Connect Kommo with 1000+ services and automate any business process"
            accentWords={["Integration", "Examples"]}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {integrationExamples.map((example, index) => (
              <FadeInSection key={index} delay={index * 150}>
                <FeatureCard
                  icon={example.icon}
                  title={example.category}
                  description={example.description}
                  variant="outlined"
                  borderColor="purple"
                  iconColor="text-purple-500"
                  hover={true}
                  size="lg"
                />
                <div className="mt-4 px-6">
                  <div className="flex flex-wrap gap-2">
                    {example.services.map((service, serviceIndex) => (
                      <span
                        key={serviceIndex}
                        className="inline-block px-3 py-1 bg-purple-100 text-purple-700 text-xs font-medium rounded-full"
                      >
                        {service}
                      </span>
                    ))}
                  </div>
                </div>
              </FadeInSection>
            ))}
          </div>

          <FadeInSection delay={1000} className="mt-16 text-center">
            <div className="bg-white rounded-xl p-8 max-w-3xl mx-auto shadow-lg border border-purple-200">
              <div className="flex items-center justify-center mb-6">
                <span className="text-purple-500 text-3xl mr-3">🔗</span>
                <h3 className="text-2xl font-bold text-gray-900">Can&apos;t Find Your Service?</h3>
              </div>
              <p className="text-gray-600 text-lg mb-6">
                We can integrate Kommo with virtually any service that has an API.
                If it&apos;s not listed above, just ask us - we&apos;ll find a way to connect it!
              </p>
              <div className="bg-purple-50 rounded-lg p-6">
                <p className="text-purple-800 font-medium">
                  <span className="font-bold">1000+</span> services available for integration through Make.com platform
                </p>
              </div>
            </div>
          </FadeInSection>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <SectionHeading
            tag="FAQ"
            title="Frequently Asked Questions"
            subtitle="Everything you need to know about Kommo CRM integrations"
            accentWords={["Questions"]}
          />

          <FadeInSection>
            <AccordionFAQ
              items={integrationFAQ}
              variant="bordered"
              allowMultiple={false}
              animated={true}
            />
          </FadeInSection>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 bg-gray-900 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-gray-800 to-gray-900"></div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <FadeInSection>
              <div>
                <div className="inline-block px-4 py-2 mb-6 bg-purple-500 bg-opacity-20 text-purple-300 rounded-full text-sm font-semibold">
                  🔗 Custom Integration Solution
                </div>
                <h2 className="text-3xl md:text-4xl font-bold mb-6 leading-tight">
                  Order <span className="text-purple-400">Custom Integration</span> for Your Business
                </h2>
                <p className="text-xl text-gray-300 mb-8 leading-relaxed">
                  Describe what needs to be integrated with what, and we&apos;ll create a tailored solution that saves you hours every day
                </p>
                <div className="space-y-4 text-gray-300">
                  <div className="flex items-center">
                    <span className="text-purple-400 mr-3">✓</span>
                    <span>Free consultation and integration roadmap</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-purple-400 mr-3">✓</span>
                    <span>Transparent pricing and timeline</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-purple-400 mr-3">✓</span>
                    <span>Ready integration scenarios catalog</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-purple-400 mr-3">✓</span>
                    <span>Ongoing support and maintenance</span>
                  </div>
                </div>
              </div>
            </FadeInSection>
            <FadeInSection delay={200}>
              <div className="bg-white rounded-xl shadow-2xl p-2">
                <ContactForm
                  title="Get Integration Quote"
                  subtitle=""
                  showGift={true}
                  giftText="✅ Yes, I want to receive the free 'Catalog of Ready Integration Scenarios for Kommo CRM'"
                  buttonText="Send Request"
                  className="bg-white text-gray-900"
                />
              </div>
            </FadeInSection>
          </div>
        </div>
      </section>
    </div>
  );
}
