'use client';

import React, { useState } from 'react';
import Button from '@/components/Button';

export default function WebhookStatusPage() {
  const [status, setStatus] = useState<Record<string, unknown> | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const testWebhook = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Test User',
          email: '<EMAIL>',
          phone: '+1234567890',
          comment: 'Test from webhook status page'
        }),
      });

      const data = await response.json();
      setStatus(data);
    } catch (error) {
      setStatus({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const checkConfig = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/test-webhook');
      const data = await response.json();
      setStatus(data);
    } catch (error) {
      setStatus({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            🔗 Webhook Status & Testing
          </h1>

          <div className="space-y-6">
            {/* Action buttons */}
            <div className="flex gap-4">
              <Button
                onClick={checkConfig}
                variant="secondary"
                isLoading={isLoading}
              >
                Check Configuration
              </Button>
              
              <Button
                onClick={testWebhook}
                variant="primary"
                isLoading={isLoading}
              >
                Test Webhook
              </Button>
            </div>

            {/* Status display */}
            {status && (
              <div className="mt-8">
                <h2 className="text-xl font-semibold mb-4">
                  {status.success ? '✅ Result' : '❌ Error'}
                </h2>
                
                <div className="bg-gray-50 rounded-lg p-4">
                  <pre className="text-sm overflow-x-auto">
                    {JSON.stringify(status, null, 2)}
                  </pre>
                </div>

                {(status as any).success && (status as any).webhookResponse && (
                  <div className="mt-4 p-4 bg-green-50 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">
                      Webhook Response:
                    </h3>
                    <p className="text-green-700">
                      Status: {(status as any).webhookResponse.status} - {(status as any).webhookResponse.body}
                    </p>
                  </div>
                )}

                {!(status as any).success && (
                  <div className="mt-4 p-4 bg-red-50 rounded-lg">
                    <h3 className="font-semibold text-red-800 mb-2">
                      Error Details:
                    </h3>
                    <p className="text-red-700">{(status as any).error}</p>
                  </div>
                )}
              </div>
            )}

            {/* Instructions */}
            <div className="mt-8 p-6 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-3">
                📋 How to use:
              </h3>
              <ul className="text-blue-700 space-y-2">
                <li>• <strong>Check Configuration</strong> - Verify webhook URL is set</li>
                <li>• <strong>Test Webhook</strong> - Send test data to Make.com</li>
                <li>• Look for &quot;Accepted&quot; response from Make.com</li>
                <li>• Check your Make.com scenario for received data</li>
              </ul>
            </div>

            {/* Current webhook URL */}
            <div className="mt-6 p-4 bg-gray-100 rounded-lg">
              <h3 className="font-semibold text-gray-800 mb-2">
                Current Webhook URL:
              </h3>
              <code className="text-sm text-gray-600 break-all">
                {process.env.NEXT_PUBLIC_WEBHOOK_URL || 'Check server logs for actual URL'}
              </code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
