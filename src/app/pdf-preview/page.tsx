'use client';

import React, { useState } from 'react';
import Button from '@/components/Button';

export default function PdfPreviewPage() {
  const [htmlContent, setHtmlContent] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);

  const generatePreview = async () => {
    setIsLoading(true);
    try {
      // Sample data for PDF generation
      const sampleData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        calculationResults: {
          deltaRevenue: 15000,
          gainPercent: 30,
          savedHours: 45,
          conversionGrowth: 30,
          initialData: {
            leads: 500,
            conversion: 8,
            avgDealSize: 1200,
            timePerLead: 15,
            niche: 'Real Estate',
            managers: 3,
            channels: ['email', 'google-ads', 'instagram']
          }
        }
      };

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sampleData),
      });

      const data = await response.json();
      if (data.htmlPreview) {
        setHtmlContent(data.htmlPreview);
      }
    } catch (error) {
      console.error('Error generating preview:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-6xl mx-auto px-4">
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            📄 PDF Report Preview
          </h1>

          <div className="mb-6">
            <Button
              onClick={generatePreview}
              variant="primary"
              isLoading={isLoading}
            >
              Generate Sample PDF Preview
            </Button>
          </div>

          <div className="text-sm text-gray-600 mb-4">
            <p>This shows how the PDF report will look when generated by Make.com.</p>
            <p>Sample data: Real Estate business, 500 leads/month, $15,000 potential increase</p>
          </div>
        </div>

        {htmlContent && (
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="bg-gray-100 px-6 py-3 border-b">
              <h2 className="text-lg font-semibold text-gray-800">
                PDF Content Preview
              </h2>
            </div>
            
            <div className="p-6">
              <div 
                className="border border-gray-200 rounded-lg overflow-hidden"
                style={{ 
                  maxHeight: '80vh', 
                  overflow: 'auto',
                  background: 'white'
                }}
              >
                <iframe
                  srcDoc={htmlContent}
                  style={{
                    width: '125%',
                    height: '800px',
                    border: 'none',
                    transform: 'scale(0.8)',
                    transformOrigin: 'top left'
                  }}
                  title="PDF Preview"
                />
              </div>
            </div>

            <div className="bg-gray-50 px-6 py-4 border-t">
              <details className="cursor-pointer">
                <summary className="font-semibold text-gray-700 mb-2">
                  View HTML Source
                </summary>
                <pre className="text-xs bg-gray-100 p-4 rounded overflow-x-auto">
                  {htmlContent}
                </pre>
              </details>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 rounded-xl p-6">
          <h3 className="font-semibold text-blue-800 mb-3">
            📋 How PDF Generation Works:
          </h3>
          <ol className="text-blue-700 space-y-2">
            <li>1. User fills ROI calculator and checks &quot;Email me PDF report&quot;</li>
            <li>2. Data is sent to Make.com webhook with HTML content</li>
            <li>3. Make.com uses &quot;HTML to PDF&quot; module to generate PDF</li>
            <li>4. PDF is emailed to user automatically</li>
            <li>5. Manager gets notification with lead details</li>
          </ol>
          
          <div className="mt-4 p-4 bg-white rounded border-l-4 border-blue-400">
            <h4 className="font-semibold text-blue-800 mb-2">Make.com Setup Required:</h4>
            <p className="text-blue-700 text-sm">
              See <code>MAKE_COM_PDF_SETUP.md</code> for detailed instructions on configuring
              the HTML to PDF conversion and email automation in your Make.com scenario.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
