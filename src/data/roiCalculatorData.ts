export const INDUSTRY_OPTIONS = [
  { value: '', label: 'Select your industry' },
  { value: 'Real Estate', label: 'Real Estate' },
  { value: 'SaaS', label: 'SaaS' },
  { value: 'Online Education', label: 'Online Education' },
  { value: 'Manufacturing', label: 'Manufacturing' },
  { value: 'E-commerce', label: 'E-commerce' },
  { value: 'Healthcare', label: 'Healthcare' },
  { value: 'Financial Services', label: 'Financial Services' },
  { value: 'Consulting', label: 'Consulting' },
  { value: 'Marketing Agency', label: 'Marketing Agency' },
  { value: 'Retail', label: 'Retail' },
  { value: 'Automotive', label: 'Automotive' },
  { value: 'Travel & Tourism', label: 'Travel & Tourism' },
  { value: 'Other', label: 'Other' },
];

export const CHANNEL_OPTIONS = [
  { value: 'website-form', label: 'Website form' },
  { value: 'instagram', label: 'Instagram' },
  { value: 'facebook', label: 'Facebook' },
  { value: 'linkedin', label: 'LinkedIn' },
  { value: 'google-ads', label: 'Google Ads' },
  { value: 'email', label: 'Email marketing' },
  { value: 'phone-calls', label: 'Phone calls' },
  { value: 'referrals', label: 'Referrals' },
  { value: 'events', label: 'Events/Webinars' },
  { value: 'content-marketing', label: 'Content marketing' },
  { value: 'seo', label: 'SEO/Organic search' },
  { value: 'other', label: 'Other' },
];

export const FIELD_TOOLTIPS = {
  leads: "Shows the scale of your business and the potential volume of data for processing.",
  conversion: "Reflects the effectiveness of your current funnel. Automation can significantly improve it.",
  avgDealSize: "Determines the value of each transaction. Influences the total potential revenue.",
  timePerLead: "The more time spent per lead, the higher the impact of automation. Automation frees up valuable hours.",
  niche: "Helps us tailor the calculation and examples more precisely to your business specifics.",
  managers: "Allows us to estimate the scale of routine operations and the potential for time savings across your team.",
  channels: "Specifies where automation can optimize your lead processing workflow.",
};

// Market benchmarks by industry
export const INDUSTRY_BENCHMARKS = {
  'Real Estate': { avgConversion: 12, description: 'Real estate typically sees 8-15% conversion rates' },
  'SaaS': { avgConversion: 15, description: 'SaaS companies average 10-20% conversion rates' },
  'Online Education': { avgConversion: 8, description: 'Online education sees 5-12% conversion rates' },
  'Manufacturing': { avgConversion: 10, description: 'Manufacturing B2B typically sees 8-15% conversion rates' },
  'E-commerce': { avgConversion: 3, description: 'E-commerce averages 2-5% conversion rates' },
  'Healthcare': { avgConversion: 18, description: 'Healthcare services see 15-25% conversion rates' },
  'Financial Services': { avgConversion: 13, description: 'Financial services average 10-18% conversion rates' },
  'Consulting': { avgConversion: 20, description: 'Consulting services see 15-30% conversion rates' },
  'Marketing Agency': { avgConversion: 16, description: 'Marketing agencies average 12-20% conversion rates' },
  'Retail': { avgConversion: 5, description: 'Retail typically sees 3-8% conversion rates' },
  'Automotive': { avgConversion: 14, description: 'Automotive sales see 10-18% conversion rates' },
  'Travel & Tourism': { avgConversion: 7, description: 'Travel industry averages 5-10% conversion rates' },
};

// Case Studies Data
export interface CaseStudy {
  id: string;
  title: string;
  niche: string;
  before: {
    leads: number;
    conversion: number;
    revenue: number;
    timePerLead: number;
  };
  after: {
    conversion: number;
    revenue: number;
    savedHours: number;
  };
  quote: string;
  author: string;
  position: string;
}

export const CASE_STUDIES: CaseStudy[] = [
  {
    id: 'horizon-real-estate',
    title: 'Case Study: "Horizon" Real Estate Agency',
    niche: 'Real Estate',
    before: {
      leads: 100,
      conversion: 5,
      revenue: 50000,
      timePerLead: 30
    },
    after: {
      conversion: 10,
      revenue: 100000,
      savedHours: 50
    },
    quote: 'Automation allowed us to process twice as many leads without increasing staff. This is a breakthrough!',
    author: 'Anna Smirnova',
    position: 'Sales Director'
  },
  {
    id: 'techflow-saas',
    title: 'Case Study: "TechFlow" SaaS Platform',
    niche: 'SaaS',
    before: {
      leads: 250,
      conversion: 12,
      revenue: 90000,
      timePerLead: 20
    },
    after: {
      conversion: 18,
      revenue: 135000,
      savedHours: 83
    },
    quote: 'Our response time improved from hours to minutes. Customer satisfaction and conversion rates skyrocketed.',
    author: 'Michael Chen',
    position: 'Head of Sales'
  },
  {
    id: 'edumaster-online',
    title: 'Case Study: "EduMaster" Online Education',
    niche: 'Online Education',
    before: {
      leads: 180,
      conversion: 8,
      revenue: 28800,
      timePerLead: 25
    },
    after: {
      conversion: 12,
      revenue: 43200,
      savedHours: 75
    },
    quote: 'Automated follow-ups helped us nurture leads better. Our course enrollment increased by 50%.',
    author: 'Sarah Johnson',
    position: 'Marketing Manager'
  },
  {
    id: 'shopify-store',
    title: 'Case Study: "StyleHub" E-commerce Store',
    niche: 'E-commerce',
    before: {
      leads: 500,
      conversion: 2.5,
      revenue: 62500,
      timePerLead: 15
    },
    after: {
      conversion: 4,
      revenue: 100000,
      savedHours: 125
    },
    quote: 'Abandoned cart recovery and automated email sequences transformed our business. ROI was immediate.',
    author: 'David Rodriguez',
    position: 'E-commerce Manager'
  }
];
