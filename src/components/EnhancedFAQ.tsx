'use client';

import React, { useState } from 'react';
import FadeInSection from '@/components/FadeInSection';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

interface FAQCategory {
  name: string;
  items: FAQItem[];
}

const faqData: FAQCategory[] = [
  {
    name: "Organizational Challenges",
    items: [
      {
        id: 'resistance',
        category: 'organizational',
        question: 'What if employees resist CRM implementation?',
        answer: `We've handled this challenge hundreds of times. Our proven approach includes:

**Phase 1: Understanding & Buy-in**
• Individual interviews with key team members to understand concerns
• Demonstrating immediate personal benefits (less manual work, clearer priorities)
• Involving team leaders in system design decisions

**Phase 2: Gradual Implementation**
• Start with willing early adopters as champions
• Implement features that solve immediate pain points first
• Show quick wins within the first week

**Real Example:** A real estate agency with 15 agents initially had 70% resistance. After showing how automated lead distribution saved 2 hours daily per agent, adoption reached 95% within 3 weeks.

**Our Success Rate:** 94% of teams achieve full adoption within 30 days using our methodology.`
      },
      {
        id: 'roi-timeline',
        category: 'organizational',
        question: 'How quickly will we see ROI from Kommo implementation?',
        answer: `**Immediate Results (Week 1-2):**
• No more lost leads through automation
• 30-50% reduction in manual data entry
• Clear visibility into sales pipeline

**Short-term ROI (Month 1-3):**
• 15-25% increase in lead conversion rates
• 20-40% time savings for sales managers
• Reduced customer response time from hours to minutes

**Long-term ROI (Month 3-12):**
• 25-40% increase in overall sales efficiency
• 60% reduction in administrative tasks
• Predictable revenue forecasting

**Real Case Study:** TechStart Inc. (SaaS, 25 employees) saw $45,000 additional revenue in the first quarter after $3,000 implementation investment - 15x ROI.

**Average Payback Period:** 2-4 months for most businesses.`
      },
      {
        id: 'pilot-project',
        category: 'organizational',
        question: 'Can we start with a pilot project before full implementation?',
        answer: `**Yes! We offer several pilot options:**

**Mini-Pilot (2 weeks, $500):**
• Setup for 1 sales funnel + 2 integrations
• 3 users, basic automation
• Perfect for testing core functionality

**Department Pilot (1 month, $1,200):**
• Full setup for one department/team
• All integrations and automations
• Includes training and support
• Can be upgraded to full implementation

**Pilot Benefits:**
• Risk-free evaluation of system fit
• Team gets hands-on experience
• Identifies specific customization needs
• Pilot cost deducted from full implementation

**Success Rate:** 87% of pilot projects convert to full implementation within 60 days.`
      }
    ]
  },
  {
    name: "Technical & Security",
    items: [
      {
        id: 'data-security',
        category: 'technical',
        question: 'How do you ensure data security during integrations?',
        answer: `**Our Security Framework:**

**Certification & Compliance:**
• ISO 27001 certified processes
• GDPR compliant data handling
• SOC 2 Type II audited procedures

**Technical Safeguards:**
• Encrypted data transmission (TLS 1.3)
• API key rotation and access controls
• Regular security audits and penetration testing
• Backup verification before any migration

**Access Control:**
• Role-based permissions setup
• Multi-factor authentication implementation
• Audit logs for all data access
• Immediate access revocation capabilities

**Data Protection:**
• Automated daily backups with 30-day retention
• Geographic data redundancy
• Point-in-time recovery options
• Zero-downtime migration protocols

**Compliance:** We maintain certifications for healthcare (HIPAA), finance (PCI DSS), and EU data protection (GDPR).`
      },
      {
        id: 'process-changes',
        category: 'technical',
        question: 'What if our business processes change after implementation?',
        answer: `**System Flexibility:**
Kommo is designed for growth and change. We build adaptable architectures that evolve with your business.

**Modification Process:**
• **Minor Changes (field additions, workflow tweaks):** 1-3 days, often included in support
• **Medium Changes (new integrations, funnel restructuring):** 1-2 weeks, $500-$1,500
• **Major Changes (complete process overhaul):** 2-4 weeks, quoted individually

**Included in All Plans:**
• First 3 months: unlimited minor modifications
• Quarterly optimization sessions (Enterprise plan)
• Process evolution consulting

**Real Example:** E-commerce client added B2B sales channel 6 months post-implementation. We integrated new workflows, pricing models, and reporting in 10 days for $800.

**Future-Proofing:** We document all customizations and provide change management guidelines.`
      },
      {
        id: 'common-mistakes',
        category: 'technical',
        question: 'What mistakes do companies make with CRM implementation, and how do you prevent them?',
        answer: `**Top 5 Implementation Mistakes & Our Prevention:**

**1. Over-complicating from Day 1**
• Mistake: Trying to automate everything immediately
• Our Approach: Start simple, add complexity gradually
• Result: 40% faster adoption rates

**2. Insufficient User Training**
• Mistake: One-time training session
• Our Approach: Multi-phase training + ongoing support + video library
• Result: 95% user proficiency within 30 days

**3. Poor Data Migration**
• Mistake: Importing dirty, duplicate data
• Our Approach: Data audit, cleaning, and validation before migration
• Result: 99.8% data accuracy post-migration

**4. Ignoring Mobile Users**
• Mistake: Desktop-only optimization
• Our Approach: Mobile-first setup and training
• Result: 60% higher field team adoption

**5. No Success Metrics**
• Mistake: No measurement of implementation success
• Our Approach: Define KPIs upfront, weekly progress reports
• Result: Clear ROI demonstration within 60 days`
      }
    ]
  },
  {
    name: "Custom Solutions & Integration",
    items: [
      {
        id: 'custom-integrations',
        category: 'custom',
        question: 'Can you integrate Kommo with non-standard services like internal ERP or industry-specific tools?',
        answer: `**Yes, we specialize in custom integrations:**

**Our Integration Expertise:**
• 200+ successful custom integrations completed
• Experience with manufacturing ERP (SAP, Oracle, 1C)
• Healthcare systems (EMR, practice management)
• Real estate platforms (MLS, property management)
• E-commerce platforms (custom shopping carts, inventory systems)

**Integration Process:**
• **Discovery:** API documentation review and system analysis
• **Development:** Custom middleware or direct API connections
• **Testing:** Sandbox environment with real data scenarios
• **Deployment:** Phased rollout with monitoring

**Recent Custom Projects:**
• Manufacturing company: Kommo + custom inventory system + accounting software
• Medical clinic: Kommo + patient management system + billing platform
• Law firm: Kommo + case management system + document automation

**Timeline & Cost:**
• Simple integrations: 1-2 weeks, $1,500-$3,000
• Complex integrations: 3-6 weeks, $5,000-$15,000
• Ongoing maintenance: $200-$500/month

**Guarantee:** All custom integrations include 6 months of free maintenance and updates.`
      },
      {
        id: 'distributed-teams',
        category: 'custom',
        question: 'How do you work with distributed or international teams?',
        answer: `**Global Implementation Capabilities:**

**Multi-timezone Support:**
• Training sessions scheduled across all time zones
• 24/7 support during implementation period
• Regional implementation managers for major markets

**Language & Localization:**
• Interface setup in 15+ languages
• Localized training materials and documentation
• Cultural adaptation of sales processes

**Remote Implementation Process:**
• Virtual workshops and training sessions
• Screen-sharing setup and configuration
• Cloud-based collaboration tools
• Regional data compliance (GDPR, CCPA, etc.)

**Success Stories:**
• **Global SaaS Company:** 45 users across 12 countries, 3 languages
• **International Consulting:** Teams in US, UK, Australia, Singapore
• **Remote-First Startup:** 100% distributed team, 8 time zones

**Communication Channels:**
• Slack/Teams integration for real-time support
• Regional phone numbers for local support
• Video conferencing for training and troubleshooting
• Multilingual documentation and help resources

**Implementation Timeline:** Typically 2-3 weeks longer for international teams to accommodate scheduling and coordination.`
      },
      {
        id: 'partial-automation',
        category: 'custom',
        question: 'What if we already have some automated processes in place?',
        answer: `**We excel at hybrid implementations:**

**Current System Audit:**
• Comprehensive review of existing automation
• Identification of integration opportunities vs. replacements
• Gap analysis and improvement recommendations
• ROI assessment for each existing tool

**Integration Strategies:**
• **Keep & Connect:** Maintain valuable existing tools, integrate with Kommo
• **Migrate & Improve:** Transfer processes to Kommo with enhancements
• **Parallel Run:** Gradual transition with zero downtime
• **Hybrid Approach:** Best of both worlds solution

**Minimizing Disruption:**
• Phased implementation to avoid business interruption
• Data synchronization during transition period
• Staff training on new vs. existing processes
• Rollback procedures if needed

**Real Example:** Marketing agency had automated email campaigns (MailChimp) and project management (Asana). We integrated both with Kommo, creating unified client journey tracking while preserving their existing workflows.

**Common Scenarios We Handle:**
• Existing email marketing automation
• Established accounting software connections
• Current telephony systems
• Legacy database systems
• Partial CRM implementations

**Outcome:** 95% of clients keep at least 2-3 existing tools while gaining unified visibility through Kommo.`
      }
    ]
  },
  {
    name: "Performance & Support",
    items: [
      {
        id: 'measuring-success',
        category: 'performance',
        question: 'How do you measure implementation effectiveness?',
        answer: `**Comprehensive Success Metrics:**

**Week 1-2 Metrics:**
• User login frequency and session duration
• Data entry completion rates
• Feature adoption tracking
• Support ticket volume and resolution time

**Month 1-3 KPIs:**
• Lead response time improvement
• Conversion rate changes by stage
• Sales cycle length reduction
• Manager time savings quantification

**Long-term Success Indicators:**
• Revenue growth attribution
• Customer satisfaction scores
• Team productivity metrics
• Process automation ROI

**Reporting & Analytics:**
• Weekly implementation progress reports
• Monthly performance dashboards
• Quarterly business impact assessments
• Annual optimization recommendations

**Benchmarking:**
• Industry-specific performance comparisons
• Best practice implementation scoring
• Continuous improvement recommendations

**Success Guarantee:** If key metrics don't improve within 90 days, we provide additional optimization at no cost.`
      },
      {
        id: 'ongoing-support',
        category: 'performance',
        question: 'What support options are available after the free period?',
        answer: `**Flexible Support Plans:**

**Essential Support ($200/month):**
• Email support (24-48 hour response)
• Basic troubleshooting and bug fixes
• Monthly system health check
• Access to knowledge base and video tutorials

**Professional Support ($500/month):**
• Priority email + phone support (4-8 hour response)
• Monthly optimization session (1 hour)
• Quarterly performance review
• Minor customizations included (up to 5 hours/month)

**Enterprise Support ($1,200/month):**
• Dedicated account manager
• 2-hour response SLA during business hours
• Weekly check-ins and proactive monitoring
• Unlimited minor modifications
• Priority access to new features

**Pay-per-Incident:**
• One-time support: $150/hour
• Project-based modifications: quoted individually
• Emergency support: $300/hour (same-day response)

**What's Included in All Plans:**
• System updates and security patches
• Integration maintenance
• User training refreshers
• Performance monitoring and alerts

**Average Client Choice:** 70% choose Professional Support for optimal balance of cost and service level.`
      },
      {
        id: 'readiness-assessment',
        category: 'performance',
        question: 'Can we get a readiness checklist or calculator for self-assessment?',
        answer: `**Free CRM Readiness Tools:**

**Self-Assessment Checklist (Available Now):**
• 25-point readiness evaluation
• Team preparedness scoring
• Technical requirements verification
• Process maturity assessment
• Implementation timeline estimation

**ROI Calculator (Interactive Tool):**
• Current process cost analysis
• Projected efficiency gains
• Implementation investment breakdown
• Payback period calculation
• 3-year ROI projection

**Business Process Audit Template:**
• Sales funnel mapping worksheet
• Integration requirements checklist
• Team training needs assessment
• Success metrics definition guide

**How to Access:**
• Download from our resource center
• Request personalized assessment
• Schedule 30-minute evaluation call
• Get expert review of your results

**Bonus Resources:**
• CRM Implementation Best Practices Guide (47 pages)
• Industry-specific setup templates
• Change management playbook
• User adoption strategies

**Expert Review:** Submit your completed assessment for free expert analysis and personalized recommendations within 48 hours.

**Next Steps:** Based on your assessment, we'll recommend the optimal implementation approach and timeline for your specific situation.`
      }
    ]
  }
];

interface EnhancedFAQProps {
  className?: string;
}

const EnhancedFAQ: React.FC<EnhancedFAQProps> = ({ className = '' }) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };

  const formatAnswer = (answer: string) => {
    return answer.split('\n').map((line, index) => {
      if (line.startsWith('**') && line.endsWith('**')) {
        return (
          <h4 key={index} className="font-semibold text-gray-900 mt-4 mb-2">
            {line.replace(/\*\*/g, '')}
          </h4>
        );
      }
      if (line.startsWith('•')) {
        return (
          <li key={index} className="ml-4 text-gray-700">
            {line.substring(1).trim()}
          </li>
        );
      }
      if (line.trim() === '') {
        return <br key={index} />;
      }
      return (
        <p key={index} className="text-gray-700 mb-2">
          {line}
        </p>
      );
    });
  };

  return (
    <section className={`py-20 bg-white ${className}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Real answers to real concerns from our 10+ years of CRM implementation experience
            </p>
          </div>
        </FadeInSection>

        {faqData.map((category, categoryIndex) => (
          <FadeInSection key={category.name} delay={200 + categoryIndex * 100}>
            <div className="mb-12">
              <h3 className="text-xl font-semibold text-blue-600 mb-6 border-b border-blue-200 pb-2">
                {category.name}
              </h3>
              
              <div className="space-y-4">
                {category.items.map((item) => (
                  <div
                    key={item.id}
                    className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
                  >
                    <button
                      onClick={() => toggleItem(item.id)}
                      className="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex justify-between items-center"
                    >
                      <h4 className="text-lg font-semibold text-gray-900 pr-4">
                        {item.question}
                      </h4>
                      <span className="text-2xl text-gray-500 flex-shrink-0">
                        {openItems.has(item.id) ? '−' : '+'}
                      </span>
                    </button>
                    
                    {openItems.has(item.id) && (
                      <div className="px-6 py-6 bg-white border-t border-gray-200">
                        <div className="prose prose-blue max-w-none">
                          {formatAnswer(item.answer)}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </FadeInSection>
        ))}

        {/* CTA Section */}
        <FadeInSection delay={600}>
          <div className="mt-16 bg-blue-50 rounded-xl p-8 text-center border border-blue-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Get personalized answers and a free CRM readiness assessment
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Schedule Free Consultation
            </button>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default EnhancedFAQ;
