'use client';

import React, { useState } from 'react';
import FadeInSection from '@/components/FadeInSection';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    id: 'implementation-time',
    question: 'How long does it take to implement Kommo CRM?',
    answer: `On average, 14 to 30 business days, depending on process complexity and the number of channels (website, telephony, messengers). Simple projects can be launched in a week.`
  },
  {
    id: 'first-results',
    question: 'When will I see the first results?',
    answer: `Within the first week after launch: automations kick in, leads stop getting lost, and managers start tracking deals. After that—conversion growth and better control.`
  },
  {
    id: 'what-needed',
    question: 'What do you need from us?',
    answer: `Just the involvement of a responsible manager or leader at the start—for interviews and structure approval. We handle everything else.`
  },
  {
    id: 'existing-kommo',
    question: 'What if we already have Kommo?',
    answer: `We often work with "tired" CRMs: we audit, find errors, optimize pipelines, and build new logic. Sometimes it's easier to redo than to "fix."`
  },
  {
    id: 'turnkey-solution',
    question: 'Do you deliver a turnkey solution?',
    answer: `Yes. From audit to setup, integrations, training, and support. You'll have a ready-to-use CRM you can start working with immediately.`
  },
  {
    id: 'automations',
    question: 'Will there be automations?',
    answer: `Yes. We set up pipelines with triggers, bots, and notifications. For example: website lead → auto-reply → task → notification → deal → stage → report. All without manual routine.`
  },
  {
    id: 'team-training',
    question: 'Do you train the team?',
    answer: `Absolutely. We'll run a live Zoom session or record instructions for distributed teams. Plus, we provide a video guide for each stage.`
  },
  {
    id: 'support-issues',
    question: 'What if something breaks?',
    answer: `We offer post-launch support. If needed, we can provide ongoing CRM maintenance (optional).`
  },
  {
    id: 'implementation-cost',
    question: 'How much does implementation cost?',
    answer: `The price depends on the scope, number of channels, and logic complexity. We tailor the solution to your needs and budget—after a free audit.`
  },
  {
    id: 'connect-channels',
    question: 'Can you connect the website, email, and messengers?',
    answer: `Yes, all included in the implementation package. Kommo connects all communications in one system. No more searching for conversations across platforms.`
  }
];

interface EnhancedFAQProps {
  className?: string;
}

const EnhancedFAQ: React.FC<EnhancedFAQProps> = ({ className = '' }) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };



  return (
    <section className={`py-20 bg-white ${className}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Common questions from small and medium businesses about Kommo CRM implementation
            </p>
          </div>
        </FadeInSection>

        <div className="space-y-4">
          {faqData.map((item, index) => (
            <FadeInSection key={item.id} delay={200 + index * 50}>
              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex justify-between items-center"
                >
                  <h4 className="text-lg font-semibold text-gray-900 pr-4">
                    {item.question}
                  </h4>
                  <span className="text-2xl text-gray-500 flex-shrink-0">
                    {openItems.has(item.id) ? '−' : '+'}
                  </span>
                </button>

                {openItems.has(item.id) && (
                  <div className="px-6 py-6 bg-white border-t border-gray-200">
                    <p className="text-gray-700 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                )}
              </div>
            </FadeInSection>
          ))}
        </div>

        {/* CTA Section */}
        <FadeInSection delay={600}>
          <div className="mt-16 bg-blue-50 rounded-xl p-8 text-center border border-blue-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Get personalized answers and a free CRM readiness assessment
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Schedule Free Consultation
            </button>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default EnhancedFAQ;
