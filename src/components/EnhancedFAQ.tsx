'use client';

import React, { useState } from 'react';
import FadeInSection from '@/components/FadeInSection';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    id: 'resistance',
    question: 'What if my team resists using the CRM?',
    answer: `This is common! We start with willing team members first and show immediate benefits like automated lead distribution and less manual work. Most teams see the value within the first week when they realize it saves 1-2 hours daily. We provide hands-on training and support until everyone is comfortable.`
  },
  {
    id: 'roi-timeline',
    question: 'How quickly will we see results?',
    answer: `You'll see immediate improvements in the first week - no more lost leads and less manual work. Most businesses see 15-25% better conversion rates within 1-3 months. The system typically pays for itself in 2-4 months through increased sales and time savings.`
  },
  {
    id: 'implementation-time',
    question: 'How long does implementation take?',
    answer: `For small businesses: 1-2 weeks. For medium businesses: 2-4 weeks. We work around your schedule and ensure minimal disruption to your daily operations. You can keep working normally while we set everything up.`
  },
  {
    id: 'data-security',
    question: 'Is our data safe?',
    answer: `Yes, absolutely. We use bank-level security with encrypted data transmission and secure backups. Your data stays on Kommo's certified servers with role-based access controls. We're GDPR compliant and follow all data protection standards.`
  },
  {
    id: 'process-changes',
    question: 'What if we need changes after implementation?',
    answer: `No problem! Small changes like adding fields or tweaking workflows are usually free in the first 3 months. Bigger changes like new integrations typically cost $500-$1,500 and take 1-2 weeks. We build flexible systems that grow with your business.`
  },
  {
    id: 'integrations',
    question: 'Can you connect our existing tools?',
    answer: `Yes! We connect most popular business tools: your website, email, WhatsApp, accounting software, and more. If you have something unique, we can usually build a custom connection. Most integrations are included in our standard packages.`
  },
  {
    id: 'training',
    question: 'How do you train our team?',
    answer: `We provide live online training sessions, step-by-step video guides, and written materials. Training is included in all packages. We also offer follow-up sessions if needed and ongoing support to answer questions.`
  },
  {
    id: 'cost',
    question: 'How much does implementation cost?',
    answer: `We have three main packages: Basic ($1,200), Standard ($3,000), and Enterprise ($6,000+). The price depends on your business size, number of integrations, and complexity. We always start with a free audit to recommend the right plan for your budget.`
  },
  {
    id: 'support',
    question: 'What support do we get after implementation?',
    answer: `All packages include 30 days of free support. After that, we offer ongoing support plans starting at $200/month for email support, or $500/month for priority phone and email support with monthly optimization sessions.`
  },
  {
    id: 'existing-crm',
    question: 'We already have a CRM. Can you help us switch?',
    answer: `Absolutely! We help migrate your data, contacts, and deals from your current system to Kommo. We'll also analyze what's working and what isn't, then improve your processes during the switch. Data migration is included in all our packages.`
  },
  {
    id: 'mobile',
    question: 'Does it work on mobile?',
    answer: `Yes! Kommo works perfectly on phones and tablets. Your team can manage leads, make calls, and update deals from anywhere. We set up mobile access and train your team on mobile features during implementation.`
  },
  {
    id: 'pilot',
    question: 'Can we try it first before committing?',
    answer: `Yes! We offer a 2-week mini-pilot for $500 where we set up basic functionality for 3 users. If you decide to proceed with full implementation, the pilot cost is deducted from your final price. Most pilots convert to full projects.`
  }
];

interface EnhancedFAQProps {
  className?: string;
}

const EnhancedFAQ: React.FC<EnhancedFAQProps> = ({ className = '' }) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set());

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      newOpenItems.add(id);
    }
    setOpenItems(newOpenItems);
  };



  return (
    <section className={`py-20 bg-white ${className}`}>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Common questions from small and medium businesses about Kommo CRM implementation
            </p>
          </div>
        </FadeInSection>

        <div className="space-y-4">
          {faqData.map((item, index) => (
            <FadeInSection key={item.id} delay={200 + index * 50}>
              <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                <button
                  onClick={() => toggleItem(item.id)}
                  className="w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-colors flex justify-between items-center"
                >
                  <h4 className="text-lg font-semibold text-gray-900 pr-4">
                    {item.question}
                  </h4>
                  <span className="text-2xl text-gray-500 flex-shrink-0">
                    {openItems.has(item.id) ? '−' : '+'}
                  </span>
                </button>

                {openItems.has(item.id) && (
                  <div className="px-6 py-6 bg-white border-t border-gray-200">
                    <p className="text-gray-700 leading-relaxed">
                      {item.answer}
                    </p>
                  </div>
                )}
              </div>
            </FadeInSection>
          ))}
        </div>

        {/* CTA Section */}
        <FadeInSection delay={600}>
          <div className="mt-16 bg-blue-50 rounded-xl p-8 text-center border border-blue-200">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Still have questions?
            </h3>
            <p className="text-gray-600 mb-6">
              Get personalized answers and a free CRM readiness assessment
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
              Schedule Free Consultation
            </button>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default EnhancedFAQ;
