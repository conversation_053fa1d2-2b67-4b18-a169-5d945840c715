import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface Industry {
  id: string;
  name: string;
  description: string;
  automation: string;
  icon: string;
  href: string;
  colorScheme: {
    gradient: string;
    button: string;
    buttonHover: string;
  };
}

interface IndustryCardProps {
  industry: Industry;
  className?: string;
  delay?: number;
}

const IndustryCard: React.FC<IndustryCardProps> = ({
  industry,
  className,
  delay = 0
}) => {
  return (
    <div
      className={cn(
        'group bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-500 hover:shadow-2xl hover:-translate-y-3 h-full flex flex-col border border-gray-100 hover:border-gray-200',
        className
      )}
      style={{
        animationDelay: `${delay}ms`
      }}
    >
      {/* Header with gradient background and icon */}
      <div className={cn(
        'relative h-32 sm:h-36 flex items-center justify-center text-white overflow-hidden',
        industry.colorScheme.gradient
      )}>
        <div className="absolute inset-0 bg-black opacity-10"></div>

        {/* Animated background pattern */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-white/20 to-transparent"></div>
        </div>

        <div className="relative z-10 text-center">
          <div className="text-4xl sm:text-5xl mb-2 transform group-hover:scale-125 group-hover:rotate-12 transition-all duration-500 filter drop-shadow-lg">
            {industry.icon}
          </div>
        </div>

        {/* Enhanced decorative elements */}
        <div className="absolute top-3 right-3 sm:top-4 sm:right-4 w-16 h-16 sm:w-20 sm:h-20 bg-white opacity-10 rounded-full transform group-hover:scale-110 transition-transform duration-500"></div>
        <div className="absolute bottom-2 left-3 sm:left-4 w-8 h-8 sm:w-10 sm:h-10 bg-white opacity-20 rounded-full transform group-hover:scale-125 transition-transform duration-500"></div>
        <div className="absolute top-1/2 left-2 w-5 h-5 sm:w-6 sm:h-6 bg-white opacity-15 rounded-full transform group-hover:translate-x-2 transition-transform duration-500"></div>

        {/* Gradient overlay for better text contrast */}
        <div className="absolute bottom-0 left-0 right-0 h-6 sm:h-8 bg-gradient-to-t from-black/20 to-transparent"></div>
      </div>

      {/* Content */}
      <div className="flex-1 p-4 sm:p-6 flex flex-col">
        <h3 className="text-lg sm:text-xl font-bold text-gray-900 mb-3 sm:mb-4 group-hover:text-blue-600 transition-colors duration-300 leading-tight">
          {industry.name}
        </h3>

        <div className="flex-1 space-y-4 sm:space-y-5">
          <div className="bg-red-50 border-l-4 border-red-400 p-2.5 sm:p-3 rounded-r-lg">
            <h4 className="text-xs sm:text-sm font-bold text-red-700 mb-1.5 sm:mb-2 uppercase tracking-wide flex items-center">
              <span className="mr-1.5 sm:mr-2">⚠️</span>
              Challenges
            </h4>
            <p className="text-red-600 text-xs sm:text-sm leading-relaxed font-medium">
              {industry.description}
            </p>
          </div>

          <div className="bg-green-50 border-l-4 border-green-400 p-2.5 sm:p-3 rounded-r-lg">
            <h4 className="text-xs sm:text-sm font-bold text-green-700 mb-1.5 sm:mb-2 uppercase tracking-wide flex items-center">
              <span className="mr-1.5 sm:mr-2">✅</span>
              Our Solutions
            </h4>
            <p className="text-green-600 text-xs sm:text-sm leading-relaxed font-medium">
              {industry.automation}
            </p>
          </div>
        </div>

        {/* CTA Button */}
        <div className="mt-4 sm:mt-6 pt-3 sm:pt-4 border-t border-gray-100">
          <Link href={industry.href} className="block">
            <div
              className={cn(
                'w-full px-6 sm:px-8 py-3 sm:py-4 text-center font-bold text-base sm:text-lg rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-2xl cursor-pointer relative overflow-hidden group/button',
                industry.colorScheme.button.includes('white')
                  ? 'bg-white text-gray-800 hover:bg-gray-50 border-2 border-gray-300 hover:border-gray-400'
                  : 'text-white shadow-lg',
                industry.colorScheme.button
              )}
            >
              {/* Hover overlay effect */}
              <div className="absolute inset-0 bg-white opacity-0 group-hover/button:opacity-10 transition-opacity duration-300"></div>

              <span className="relative z-10 flex items-center justify-center">
                Learn More
                <span className="ml-2 transform group-hover/button:translate-x-1 transition-transform duration-300">
                  →
                </span>
              </span>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default IndustryCard;
