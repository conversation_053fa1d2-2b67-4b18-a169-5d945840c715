import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ResultMetric {
  value: string;
  label: string;
  description: string;
  color: 'green' | 'blue' | 'purple' | 'orange';
  icon: string;
}

const results: ResultMetric[] = [
  {
    value: '+40%',
    label: 'More Property Viewings',
    description: 'Increased qualified leads and better follow-up',
    color: 'green',
    icon: '🏠'
  },
  {
    value: '95%',
    label: 'CRM Adoption Rate',
    description: 'Managers actively use the system daily',
    color: 'blue',
    icon: '📱'
  },
  {
    value: '-50%',
    label: 'Less Routine Tasks',
    description: 'Automated workflows save time',
    color: 'purple',
    icon: '⚡'
  },
  {
    value: '+25%',
    label: 'Conversion Rate',
    description: 'Better lead nurturing and follow-up',
    color: 'orange',
    icon: '📈'
  }
];

const testimonials = [
  {
    quote: "Before Kommo, we lost about 30% of leads due to poor follow-up. Now every client is tracked, and we've increased our closing rate significantly.",
    author: "<PERSON>",
    position: "Real Estate Agency Owner",
    company: "Premium Properties LLC",
    avatar: "👩‍💼"
  },
  {
    quote: "The automated reminders and property matching features have transformed how we work. Our agents are more productive and clients are happier.",
    author: "Michael Chen",
    position: "Sales Director",
    company: "Metro Realty Group",
    avatar: "👨‍💼"
  },
  {
    quote: "Integration with MLS and automatic property uploads saved us 10+ hours per week. ROI was achieved in just 2 months.",
    author: "Lisa Rodriguez",
    position: "Operations Manager",
    company: "Coastal Real Estate",
    avatar: "👩‍💻"
  }
];

const RealEstateResults: React.FC = () => {
  const getColorClasses = (color: string) => {
    const colorMap = {
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        value: 'text-green-600',
        icon: 'bg-green-100 text-green-600'
      },
      blue: {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        value: 'text-blue-600',
        icon: 'bg-blue-100 text-blue-600'
      },
      purple: {
        bg: 'bg-purple-50',
        border: 'border-purple-200',
        value: 'text-purple-600',
        icon: 'bg-purple-100 text-purple-600'
      },
      orange: {
        bg: 'bg-orange-50',
        border: 'border-orange-200',
        value: 'text-orange-600',
        icon: 'bg-orange-100 text-orange-600'
      }
    };
    return colorMap[color as keyof typeof colorMap];
  };

  return (
    <div className="space-y-16">
      {/* Results Metrics */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Measurable Results from Real Estate CRM Implementation
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See the tangible benefits our clients achieve with Kommo CRM for real estate
          </p>
        </div>
      </FadeInSection>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {results.map((result, index) => {
          const colors = getColorClasses(result.color);
          return (
            <FadeInSection key={index} delay={index * 150}>
              <div className={cn(
                'p-6 rounded-xl border-2 transition-all duration-300 hover:shadow-lg hover:-translate-y-1',
                colors.bg,
                colors.border
              )}>
                <div className={cn(
                  'w-12 h-12 rounded-full flex items-center justify-center text-2xl mb-4',
                  colors.icon
                )}>
                  {result.icon}
                </div>
                <div className={cn('text-3xl font-bold mb-2', colors.value)}>
                  {result.value}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {result.label}
                </h3>
                <p className="text-gray-600 text-sm">
                  {result.description}
                </p>
              </div>
            </FadeInSection>
          );
        })}
      </div>


    </div>
  );
};

export default RealEstateResults;
