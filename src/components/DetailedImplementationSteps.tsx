import React from 'react';
import FadeInSection from '@/components/FadeInSection';

interface ImplementationStep {
  id: string;
  number: number;
  title: string;
  description: string;
  deliverables: string[];
  timeline: string;
  roles: string;
  icon: string;
  color: string;
}

const implementationSteps: ImplementationStep[] = [
  {
    id: 'audit',
    number: 1,
    title: 'Audit',
    description: 'We conduct interviews with stakeholders, analyze current business processes, identify bottlenecks and growth opportunities.',
    deliverables: [
      'Process mapping document',
      'Identified problems report',
      'Optimization recommendations',
      'Implementation roadmap'
    ],
    timeline: '1-2 days',
    roles: 'Client manager, business analyst, project manager',
    icon: '🔍',
    color: 'from-blue-500 to-blue-700'
  },
  {
    id: 'setup',
    number: 2,
    title: 'Setup',
    description: 'We develop custom funnels, configure fields, integrations, automations and thoroughly test the system.',
    deliverables: [
      'Working CRM with integrations',
      'Configured automations',
      'Test access for verification',
      'Data migration completed'
    ],
    timeline: '3-5 days',
    roles: 'Integrator, tester, client representative',
    icon: '⚙️',
    color: 'from-green-500 to-green-700'
  },
  {
    id: 'training',
    number: 3,
    title: 'Training',
    description: 'We conduct interactive training sessions for the team, provide video instructions and checklists, answer all questions.',
    deliverables: [
      'Conducted sessions (online/offline)',
      'Access to training materials',
      'Checklists and instructions',
      'Q&A session recordings'
    ],
    timeline: '1 day',
    roles: 'Trainer, entire client team',
    icon: '🎓',
    color: 'from-purple-500 to-purple-700'
  },
  {
    id: 'support',
    number: 4,
    title: 'Support',
    description: 'We accompany the project after launch: quickly resolve issues, refine the system, provide optimization recommendations.',
    deliverables: [
      '30/60 days of support',
      'Regular status reports',
      'Personal recommendations',
      'System optimization'
    ],
    timeline: '30-60 days',
    roles: 'Personal manager, technical specialist',
    icon: '🛡️',
    color: 'from-orange-500 to-orange-700'
  }
];

interface DetailedImplementationStepsProps {
  className?: string;
}

const DetailedImplementationSteps: React.FC<DetailedImplementationStepsProps> = ({ className = '' }) => {
  return (
    <section className={`py-20 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How the Implementation Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A clear, systematic process with defined stages and deliverables.
              You'll know exactly what to expect at each step.
            </p>
          </div>
        </FadeInSection>

        {/* Modern Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {implementationSteps.map((step, index) => (
            <FadeInSection key={step.id} delay={200 + index * 150}>
              <div className="relative group">
                {/* Card */}
                <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 h-full">
                  {/* Step Number */}
                  <div className="absolute -top-4 left-8">
                    <div className={`w-8 h-8 bg-gradient-to-br ${step.color} text-white rounded-full flex items-center justify-center text-sm font-bold shadow-lg`}>
                      {step.number}
                    </div>
                  </div>

                  {/* Icon */}
                  <div className="mb-6 pt-4">
                    <div className={`w-16 h-16 bg-gradient-to-br ${step.color} bg-opacity-10 rounded-2xl flex items-center justify-center text-3xl mb-4 group-hover:scale-110 transition-transform duration-300`}>
                      {step.icon}
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{step.title}</h3>
                    <div className="flex items-center text-xs text-gray-500 mb-4">
                      <span className="bg-gray-100 px-2 py-1 rounded-full mr-2">⏱️ {step.timeline}</span>
                    </div>
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 text-sm leading-relaxed mb-6">
                    {step.description}
                  </p>

                  {/* Deliverables */}
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-3 text-sm">Deliverables:</h4>
                    <ul className="space-y-2">
                      {step.deliverables.map((deliverable, deliverableIndex) => (
                        <li key={deliverableIndex} className="flex items-start">
                          <span className="text-green-500 mr-2 mt-0.5 text-xs">✓</span>
                          <span className="text-gray-600 text-xs leading-relaxed">{deliverable}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Team Info */}
                  <div className="mt-6 pt-4 border-t border-gray-100">
                    <div className="flex items-center text-xs text-gray-500">
                      <span className="mr-1">👥</span>
                      <span>{step.roles}</span>
                    </div>
                  </div>
                </div>

                {/* Connecting Line (Desktop only) */}
                {index < implementationSteps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-4 w-8 h-0.5 bg-gradient-to-r from-gray-300 to-gray-400 transform -translate-y-1/2 z-10">
                    <div className="absolute right-0 top-1/2 transform translate-x-1 -translate-y-1/2 w-2 h-2 bg-gray-400 rounded-full"></div>
                  </div>
                )}
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DetailedImplementationSteps;
