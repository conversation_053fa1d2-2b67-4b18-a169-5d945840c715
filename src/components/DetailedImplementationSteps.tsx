import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from '@/components/FadeInSection';
import Card from '@/components/Card';

interface ImplementationStep {
  id: string;
  number: number;
  title: string;
  description: string;
  deliverables: string[];
  timeline: string;
  achievement: string;
}

const implementationSteps: ImplementationStep[] = [
  {
    id: 'audit',
    number: 1,
    title: 'Audit & Strategy',
    description: 'We conduct interviews with stakeholders, analyze current business processes, identify bottlenecks and growth opportunities.',
    deliverables: [
      'Process mapping document',
      'Identified problems report',
      'Optimization recommendations',
      'Implementation roadmap'
    ],
    timeline: '1-3 days',
    achievement: 'Complete business process analysis delivered'
  },
  {
    id: 'setup',
    number: 2,
    title: 'Setup & Integration',
    description: 'We develop custom funnels, configure fields, integrations, automations and thoroughly test the system.',
    deliverables: [
      'Working CRM with integrations',
      'Configured automations',
      'Test access for verification',
      'Data migration completed'
    ],
    timeline: '7-15 days',
    achievement: 'Fully functional CRM system ready for use'
  },
  {
    id: 'training',
    number: 3,
    title: 'Testing & Training',
    description: 'We conduct interactive training sessions for the team, provide video instructions and checklists, answer all questions.',
    deliverables: [
      'Conducted sessions (online/offline)',
      'Access to training materials',
      'Checklists and instructions',
      'Q&A session recordings'
    ],
    timeline: '1-3 days',
    achievement: 'Team fully trained and confident with the system'
  },
  {
    id: 'support',
    number: 4,
    title: 'Launch & Support',
    description: 'We accompany the project after launch: quickly resolve issues, refine the system, provide optimization recommendations.',
    deliverables: [
      '30 days of support included',
      'Regular status reports',
      'Personal recommendations',
      'System optimization'
    ],
    timeline: '30+ days',
    achievement: 'Successful go-live with ongoing optimization'
  }
];

interface DetailedImplementationStepsProps {
  className?: string;
}

const DetailedImplementationSteps: React.FC<DetailedImplementationStepsProps> = ({ className = '' }) => {
  return (
    <section className={`py-20 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <FadeInSection>
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-gray-100 text-gray-700 rounded-full text-sm font-semibold">
              ⚙️ Implementation Process
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How the Implementation Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A clear, systematic process with defined stages and deliverables. 
              You'll know exactly what to expect at each step.
            </p>
          </div>
        </FadeInSection>

        {/* Timeline */}
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-200 via-green-200 via-purple-200 to-orange-200 hidden lg:block"></div>

          <div className="space-y-12">
            {implementationSteps.map((step, index) => (
              <FadeInSection key={step.id} delay={200 + index * 150}>
                <div className={cn(
                  "flex items-center",
                  index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"
                )}>
                  {/* Content */}
                  <div className="w-full lg:w-5/12">
                    <Card variant="elevated" className="p-6 hover:shadow-xl transition-all duration-300">
                          <div className="flex items-center mb-2">
                            <span className="bg-blue-100 text-blue-800 text-sm font-bold px-3 py-1 rounded-full mr-3">
                              Step {step.number}
                            </span>
                            <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                              ⏱️ {step.timeline}
                            </span>
                          </div>
                          <h3 className="text-lg font-bold text-gray-900 mb-2">{step.title}</h3>
                          <p className="text-gray-600 mb-4">{step.description}</p>
                          
                          {/* Deliverables */}
                          <div className="mb-4">
                            <h4 className="font-semibold text-gray-900 mb-2 text-sm">What You Get:</h4>
                            <ul className="space-y-1">
                              {step.deliverables.map((deliverable, deliverableIndex) => (
                                <li key={deliverableIndex} className="flex items-start text-sm">
                                  <span className="text-green-500 mr-2 mt-0.5">✓</span>
                                  <span className="text-gray-600">{deliverable}</span>
                                </li>
                              ))}
                            </ul>
                          </div>

                          {/* Achievement */}
                          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                            <p className="text-green-700 text-sm font-medium">
                              ✓ {step.achievement}
                            </p>
                          </div>
                    </Card>
                  </div>

                  {/* Timeline dot */}
                  <div className="hidden lg:flex w-2/12 justify-center">
                    <div className="w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  </div>

                  {/* Spacer */}
                  <div className="hidden lg:block w-5/12"></div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default DetailedImplementationSteps;
