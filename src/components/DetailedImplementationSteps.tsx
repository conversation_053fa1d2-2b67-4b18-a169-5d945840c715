import React from 'react';
import FadeInSection from '@/components/FadeInSection';

interface ImplementationStep {
  id: string;
  number: number;
  title: string;
  description: string;
  deliverables: string[];
  example: string;
  timeline: string;
  roles: string;
  icon: string;
  color: string;
}

const implementationSteps: ImplementationStep[] = [
  {
    id: 'audit',
    number: 1,
    title: 'Audit',
    description: 'We conduct interviews with stakeholders, analyze current business processes, identify bottlenecks and growth opportunities.',
    deliverables: [
      'Process mapping document',
      'Identified problems report',
      'Optimization recommendations',
      'Implementation roadmap'
    ],
    example: 'Example: A real estate agency was losing 30% of leads due to manual distribution. We identified this during audit and proposed automated lead routing.',
    timeline: '1-2 days',
    roles: 'Client manager, business analyst, project manager',
    icon: '🔍',
    color: 'from-blue-500 to-blue-700'
  },
  {
    id: 'setup',
    number: 2,
    title: 'Setup',
    description: 'We develop custom funnels, configure fields, integrations, automations and thoroughly test the system.',
    deliverables: [
      'Working CRM with integrations',
      'Configured automations',
      'Test access for verification',
      'Data migration completed'
    ],
    example: 'Example: For a consulting firm, we set up automated client onboarding: contract → welcome email → project creation → team assignment.',
    timeline: '3-5 days',
    roles: 'Integrator, tester, client representative',
    icon: '⚙️',
    color: 'from-green-500 to-green-700'
  },
  {
    id: 'training',
    number: 3,
    title: 'Training',
    description: 'We conduct interactive training sessions for the team, provide video instructions and checklists, answer all questions.',
    deliverables: [
      'Conducted sessions (online/offline)',
      'Access to training materials',
      'Checklists and instructions',
      'Q&A session recordings'
    ],
    example: 'Example: A marketing agency team learned to track campaign ROI through CRM in one 2-hour session, increasing their efficiency by 40%.',
    timeline: '1 day',
    roles: 'Trainer, entire client team',
    icon: '🎓',
    color: 'from-purple-500 to-purple-700'
  },
  {
    id: 'support',
    number: 4,
    title: 'Support',
    description: 'We accompany the project after launch: quickly resolve issues, refine the system, provide optimization recommendations.',
    deliverables: [
      '30/60 days of support',
      'Regular status reports',
      'Personal recommendations',
      'System optimization'
    ],
    example: 'Example: After launch, we helped an e-commerce client add B2B sales tracking, increasing their revenue visibility by 60%.',
    timeline: '30-60 days',
    roles: 'Personal manager, technical specialist',
    icon: '🛡️',
    color: 'from-orange-500 to-orange-700'
  }
];

interface DetailedImplementationStepsProps {
  className?: string;
}

const DetailedImplementationSteps: React.FC<DetailedImplementationStepsProps> = ({ className = '' }) => {
  return (
    <section className={`py-20 bg-gradient-to-br from-gray-50 to-blue-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How the Implementation Works
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              A clear, systematic process with defined stages and deliverables. 
              You'll know exactly what to expect at each step.
            </p>
          </div>
        </FadeInSection>

        {/* Progress Bar */}
        <FadeInSection delay={200}>
          <div className="hidden md:block mb-16">
            <div className="flex items-center justify-between max-w-4xl mx-auto">
              {implementationSteps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className="flex flex-col items-center">
                    <div className={`w-16 h-16 bg-gradient-to-br ${step.color} text-white rounded-full flex items-center justify-center text-2xl font-bold shadow-lg`}>
                      {step.number}
                    </div>
                    <span className="text-sm font-medium text-gray-700 mt-2">{step.title}</span>
                  </div>
                  {index < implementationSteps.length - 1 && (
                    <div className="flex-1 h-1 bg-gradient-to-r from-gray-300 to-gray-400 mx-4 rounded-full"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </FadeInSection>

        {/* Detailed Steps */}
        <div className="space-y-16">
          {implementationSteps.map((step, index) => (
            <FadeInSection key={step.id} delay={400 + index * 200}>
              <div className={`flex flex-col ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}>
                {/* Content */}
                <div className="flex-1">
                  <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-200">
                    <div className="flex items-center mb-6">
                      <div className={`w-12 h-12 bg-gradient-to-br ${step.color} text-white rounded-lg flex items-center justify-center text-xl mr-4`}>
                        {step.icon}
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900">
                          Step {step.number}: {step.title}
                        </h3>
                        <div className="flex items-center text-sm text-gray-600 mt-1">
                          <span className="mr-4">⏱️ {step.timeline}</span>
                          <span>👥 {step.roles}</span>
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-700 mb-6 leading-relaxed">
                      {step.description}
                    </p>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Deliverables */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">What You Get:</h4>
                        <ul className="space-y-2">
                          {step.deliverables.map((deliverable, deliverableIndex) => (
                            <li key={deliverableIndex} className="flex items-start">
                              <span className="text-green-500 mr-2 mt-0.5">✓</span>
                              <span className="text-gray-700 text-sm">{deliverable}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Example */}
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-3">Real Example:</h4>
                        <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                          <p className="text-gray-700 text-sm italic">
                            {step.example}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Visual Element */}
                <div className="flex-shrink-0">
                  <div className={`w-32 h-32 bg-gradient-to-br ${step.color} rounded-full flex items-center justify-center text-6xl text-white shadow-2xl`}>
                    {step.icon}
                  </div>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>

        {/* Summary CTA */}
        <FadeInSection delay={1200}>
          <div className="mt-16 bg-white rounded-xl p-8 shadow-lg border border-gray-200 text-center">
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Ready to Start Your Implementation?
            </h3>
            <p className="text-gray-600 mb-6">
              Get a detailed project timeline and cost estimate based on your specific needs
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors shadow-lg">
              Get Free Implementation Plan
            </button>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default DetailedImplementationSteps;
