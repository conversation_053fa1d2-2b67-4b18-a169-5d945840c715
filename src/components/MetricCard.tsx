import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface MetricCardProps {
  value: string;
  label: string;
  description?: string;
  icon?: string | React.ReactNode;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  delay?: number;
  className?: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  value,
  label,
  description,
  icon,
  color = 'blue',
  size = 'md',
  animated = true,
  delay = 0,
  className
}) => {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-50',
      border: 'border-blue-200',
      value: 'text-blue-600',
      label: 'text-blue-800',
      icon: 'text-blue-500'
    },
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      value: 'text-green-600',
      label: 'text-green-800',
      icon: 'text-green-500'
    },
    purple: {
      bg: 'bg-purple-50',
      border: 'border-purple-200',
      value: 'text-purple-600',
      label: 'text-purple-800',
      icon: 'text-purple-500'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      value: 'text-orange-600',
      label: 'text-orange-800',
      icon: 'text-orange-500'
    },
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      value: 'text-red-600',
      label: 'text-red-800',
      icon: 'text-red-500'
    }
  };

  const sizeClasses = {
    sm: {
      container: 'p-4',
      value: 'text-2xl',
      label: 'text-sm',
      description: 'text-xs',
      icon: 'text-2xl'
    },
    md: {
      container: 'p-6',
      value: 'text-3xl md:text-4xl',
      label: 'text-base',
      description: 'text-sm',
      icon: 'text-3xl'
    },
    lg: {
      container: 'p-8',
      value: 'text-4xl md:text-5xl',
      label: 'text-lg',
      description: 'text-base',
      icon: 'text-4xl'
    }
  };

  const currentColor = colorClasses[color];
  const currentSize = sizeClasses[size];

  const CardContent = () => (
    <div className={cn(
      'bg-white rounded-xl shadow-lg border text-center transition-all duration-300 hover:shadow-xl hover:-translate-y-1',
      currentColor.bg,
      currentColor.border,
      currentSize.container,
      className
    )}>
      {icon && (
        <div className={cn(
          'mb-4',
          currentSize.icon,
          currentColor.icon
        )}>
          {typeof icon === 'string' ? (
            <span>{icon}</span>
          ) : (
            icon
          )}
        </div>
      )}
      
      <div className={cn(
        'font-bold mb-2',
        currentSize.value,
        currentColor.value
      )}>
        {value}
      </div>
      
      <div className={cn(
        'font-semibold mb-2',
        currentSize.label,
        currentColor.label
      )}>
        {label}
      </div>
      
      {description && (
        <div className={cn(
          'text-gray-600',
          currentSize.description
        )}>
          {description}
        </div>
      )}
    </div>
  );

  if (animated) {
    return (
      <FadeInSection delay={delay}>
        <CardContent />
      </FadeInSection>
    );
  }

  return <CardContent />;
};

export default MetricCard;
