'use client';

import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useRoiStore } from '@/store/roiStore';
import { getInsight } from '@/utils/getInsight';
import TooltipComponent from './Tooltip';
import Button from './Button';
import NumberFormatter from './NumberFormatter';
import { INDUSTRY_BENCHMARKS } from '@/data/roiCalculatorData';

// Animated counter component
const AnimatedCounter: React.FC<{ 
  value: number; 
  prefix?: string; 
  suffix?: string;
  duration?: number;
}> = ({ value, prefix = '', suffix = '', duration = 2000 }) => {
  const [count, setCount] = useState(0);

  useEffect(() => {
    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);
      
      setCount(Math.floor(progress * value));
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    
    return () => cancelAnimationFrame(animationFrame);
  }, [value, duration]);

  return (
    <span>
      {prefix}{count.toLocaleString()}{suffix}
    </span>
  );
};

const RoiResults: React.FC = () => {
  const { results, showResults, formData, resetCalculator } = useRoiStore();

  if (!showResults || !results) return null;

  const chartData = [
    {
      name: 'Before',
      revenue: results.revenueBefore,
    },
    {
      name: 'After',
      revenue: results.revenueAfter,
    },
  ];

  const insight = getInsight(results, {
    niche: formData.niche,
    managers: formData.managers,
    channels: formData.channels,
  });

  return (
    <motion.div
      id="roi-results"
      className="mt-12 space-y-8"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.2 }}
    >
      {/* Main title with Calculate Again button */}
      <div className="text-center">
        <div className="flex flex-col md:flex-row items-center justify-between mb-6">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 md:mb-0">
            Your Growth Potential:
          </h2>
          <Button
            variant="outline"
            size="md"
            onClick={resetCalculator}
            className="px-6"
          >
            🔄 Calculate Again
          </Button>
        </div>
      </div>

      {/* Key metric - largest font */}
      <motion.div
        className="text-center bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8"
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <div className="text-5xl md:text-6xl font-bold text-green-600 mb-2">
          <NumberFormatter value={results.deltaRevenue} prefix="+$" />
        </div>
        <p className="text-xl text-gray-700">Monthly Revenue Increase</p>
      </motion.div>

      {/* Revenue chart - full width */}
      <motion.div
        className="bg-white rounded-xl shadow-lg p-6"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Comparison</h3>
          <div className="h-80 md:h-96">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  tick={{ fontSize: 14 }}
                  axisLine={{ stroke: '#e5e7eb' }}
                />
                <YAxis
                  tickFormatter={(value) => {
                    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`;
                    if (value >= 1000) return `$${(value / 1000).toFixed(0)}K`;
                    return `$${new Intl.NumberFormat('en-US').format(value)}`;
                  }}
                  tick={{ fontSize: 12 }}
                  axisLine={{ stroke: '#e5e7eb' }}
                  width={80}
                />
                <Tooltip
                  formatter={(value: number) => [`$${new Intl.NumberFormat('en-US').format(value)}`, 'Revenue']}
                  contentStyle={{
                    backgroundColor: '#f9fafb',
                    border: '1px solid #e5e7eb',
                    borderRadius: '8px',
                    fontSize: '14px'
                  }}
                />
                <Bar
                  dataKey="revenue"
                  fill="rgb(37, 99, 235)"
                  radius={[4, 4, 0, 0]}
                  maxBarSize={120}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

      {/* Metrics cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Conversion increase */}
        <motion.div
          className="bg-white rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Conversion Rate</h3>
            <TooltipComponent
              content="Based on data from 100+ automation projects in Kommo and Make.com from 2023–2025. The average conversion increase with automation is 20% to 30%."
              position="left"
            >
              <span className="text-gray-400 cursor-help">ⓘ</span>
            </TooltipComponent>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              +<AnimatedCounter value={results.conversionGrowth} />%
            </div>
            <p className="text-gray-600">Increase</p>
            <div className="mt-4 text-sm text-gray-500">
              From {results.initialData.conversion}% to{' '}
              {(results.initialData.conversion * 1.25).toFixed(1)}%
            </div>

            {/* Market benchmark */}
            {formData.niche && INDUSTRY_BENCHMARKS[formData.niche as keyof typeof INDUSTRY_BENCHMARKS] && (
              <div className="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-700">
                Industry average: {INDUSTRY_BENCHMARKS[formData.niche as keyof typeof INDUSTRY_BENCHMARKS].avgConversion}%
                <br />
                {INDUSTRY_BENCHMARKS[formData.niche as keyof typeof INDUSTRY_BENCHMARKS].description}
              </div>
            )}
          </div>
        </motion.div>

        {/* Time saved (if available) */}
        {results.savedHours && results.savedHours > 0 && (
          <motion.div
            className="bg-white rounded-xl shadow-lg p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Time Saved</h3>
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">
                <AnimatedCounter value={results.savedHours} />
              </div>
              <p className="text-gray-600">Hours/Month</p>
              <p className="text-sm text-gray-500 mt-2">Freed up for strategic work</p>
              {results.channelAnalysis?.timeSavingsBonus > 1.1 && (
                <div className="mt-3 p-2 bg-orange-50 rounded text-xs text-orange-700">
                  +{Math.round((results.channelAnalysis.timeSavingsBonus - 1) * 100)}% bonus from your channel mix
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Channel Analysis (if channels selected) */}
        {results.channelAnalysis?.selectedChannels.length > 0 && (
          <motion.div
            className="bg-white rounded-xl shadow-lg p-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
          >
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Channel Automation Potential</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Selected Channels:</span>
                <span className="font-semibold text-sm">
                  {results.channelAnalysis.selectedChannels.length}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Automation Potential:</span>
                <span className={`font-semibold px-2 py-1 rounded text-xs ${
                  results.channelAnalysis.automationPotential === 'Excellent' ? 'bg-green-100 text-green-800' :
                  results.channelAnalysis.automationPotential === 'Very High' ? 'bg-blue-100 text-blue-800' :
                  results.channelAnalysis.automationPotential === 'High' ? 'bg-indigo-100 text-indigo-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {results.channelAnalysis.automationPotential}
                </span>
              </div>
              {results.channelAnalysis.conversionBonus > 1.05 && (
                <div className="mt-3 p-2 bg-green-50 rounded text-xs text-green-700">
                  Your channel mix provides +{Math.round((results.channelAnalysis.conversionBonus - 1) * 100)}% conversion bonus
                </div>
              )}
            </div>
          </motion.div>
        )}

        {/* Deals comparison */}
        <motion.div
          className="bg-white rounded-xl shadow-lg p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.2 }}
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Deals per Month</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Before:</span>
              <span className="font-semibold">
                <AnimatedCounter value={results.dealsBefore} />
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">After:</span>
              <span className="font-semibold text-green-600">
                <AnimatedCounter value={results.dealsAfter} />
              </span>
            </div>
            <div className="border-t pt-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Increase:</span>
                <span className="font-semibold text-blue-600">
                  +<AnimatedCounter value={results.dealsAfter - results.dealsBefore} />
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      {/* AI Diagnostics */}
      <motion.div
        className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.4 }}
      >
        <h3 className="text-lg font-semibold text-gray-900 mb-3">✨ AI Diagnostics</h3>
        <p className="text-gray-700 leading-relaxed">{insight}</p>
      </motion.div>

      {/* Footnote */}
      <motion.div
        className="text-center mt-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 1.6 }}
      >
        <p className="text-sm text-gray-500">
          *Calculations are based on conservative estimates from automation projects.
          Actual results may vary depending on business specifics and current processes.
          {results.channelAnalysis?.selectedChannels.length > 0 && (
            <span className="block mt-1">
              Channel bonuses are estimated based on typical automation improvements.
            </span>
          )}
        </p>
      </motion.div>
    </motion.div>
  );
};

export default RoiResults;
