import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ComparisonItem {
  id: string;
  category: string;
  before: string;
  after: string;
  improvement?: string; // Метрика улучшения
}

interface ComparisonTableProps {
  items: ComparisonItem[];
  className?: string;
  title?: string;
  beforeLabel?: string;
  afterLabel?: string;
  variant?: 'table' | 'cards';
  animated?: boolean;
}

const ComparisonTable: React.FC<ComparisonTableProps> = ({
  items,
  className,
  title = "Before vs After",
  beforeLabel = "Before",
  afterLabel = "After",
  variant = 'table',
  animated = true
}) => {
  if (variant === 'cards') {
    return (
      <div className={cn('space-y-6', className)}>
        {title && (
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            {title}
          </h3>
        )}
        
        {items.map((item, index) => (
          <FadeInSection
            key={item.id}
            delay={animated ? index * 200 : 0}
            className="bg-white rounded-lg shadow-lg overflow-hidden"
          >
            <div className="p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                {item.category}
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Before */}
                <div className="relative">
                  <div className="flex items-center mb-3">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-red-600">
                      {beforeLabel}
                    </span>
                  </div>
                  <p className="text-gray-600 bg-red-50 p-4 rounded-lg border-l-4 border-red-500">
                    {item.before}
                  </p>
                </div>
                
                {/* After */}
                <div className="relative">
                  <div className="flex items-center mb-3">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-sm font-medium text-green-600">
                      {afterLabel}
                    </span>
                  </div>
                  <p className="text-gray-600 bg-green-50 p-4 rounded-lg border-l-4 border-green-500">
                    {item.after}
                  </p>
                  
                  {item.improvement && (
                    <div className="mt-2 text-center">
                      <span className="inline-block px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-full">
                        {item.improvement}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </FadeInSection>
        ))}
      </div>
    );
  }

  // Table variant
  return (
    <FadeInSection className={cn('overflow-hidden', className)}>
      {title && (
        <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
          {title}
        </h3>
      )}
      
      <div className="bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 border-b border-gray-200">
                <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">
                  Category
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-red-600">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    {beforeLabel}
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-sm font-semibold text-green-600">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    {afterLabel}
                  </div>
                </th>
                <th className="px-6 py-4 text-center text-sm font-semibold text-gray-900">
                  Improvement
                </th>
              </tr>
            </thead>
            <tbody>
              {items.map((item, index) => (
                <tr
                  key={item.id}
                  className={cn(
                    'border-b border-gray-200 hover:bg-gray-50 transition-colors',
                    animated && 'animate-fade-in-up'
                  )}
                  style={animated ? { animationDelay: `${index * 100}ms` } : {}}
                >
                  <td className="px-6 py-4 text-sm font-medium text-gray-900">
                    {item.category}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600">
                    <div className="bg-red-50 p-3 rounded border-l-4 border-red-500">
                      {item.before}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-600">
                    <div className="bg-green-50 p-3 rounded border-l-4 border-green-500">
                      {item.after}
                    </div>
                  </td>
                  <td className="px-6 py-4 text-center">
                    {item.improvement && (
                      <span className="inline-block px-3 py-1 bg-green-100 text-green-700 text-sm font-medium rounded-full">
                        {item.improvement}
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </FadeInSection>
  );
};

export default ComparisonTable;
