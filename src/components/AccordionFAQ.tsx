'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category?: string;
}

interface AccordionFAQProps {
  items: FAQItem[];
  className?: string;
  allowMultiple?: boolean;
  defaultOpen?: string[]; // IDs of items that should be open by default
  animated?: boolean;
  variant?: 'default' | 'bordered' | 'minimal';
}

const AccordionFAQ: React.FC<AccordionFAQProps> = ({
  items,
  className,
  allowMultiple = false,
  defaultOpen = [],
  animated = true,
  variant = 'default'
}) => {
  const [openItems, setOpenItems] = useState<Set<string>>(new Set(defaultOpen));

  const toggleItem = (id: string) => {
    const newOpenItems = new Set(openItems);
    
    if (newOpenItems.has(id)) {
      newOpenItems.delete(id);
    } else {
      if (!allowMultiple) {
        newOpenItems.clear();
      }
      newOpenItems.add(id);
    }
    
    setOpenItems(newOpenItems);
  };

  const variants = {
    default: {
      container: 'bg-white border border-gray-200 rounded-lg',
      item: 'border-b border-gray-200 last:border-b-0',
      button: 'w-full px-6 py-4 text-left hover:bg-gray-50',
      content: 'px-6 pb-4'
    },
    bordered: {
      container: '',
      item: 'bg-white border border-gray-200 rounded-lg mb-4',
      button: 'w-full px-6 py-4 text-left hover:bg-gray-50 rounded-lg',
      content: 'px-6 pb-4'
    },
    minimal: {
      container: '',
      item: 'border-b border-gray-200 last:border-b-0',
      button: 'w-full py-4 text-left hover:bg-gray-50',
      content: 'pb-4'
    }
  };

  const currentVariant = variants[variant];

  return (
    <div className={cn(currentVariant.container, className)}>
      {items.map((item, index) => {
        const isOpen = openItems.has(item.id);
        
        return (
          <FadeInSection
            key={item.id}
            delay={animated ? index * 100 : 0}
            className={currentVariant.item}
          >
            <button
              onClick={() => toggleItem(item.id)}
              className={cn(
                'flex items-center justify-between transition-colors duration-200',
                currentVariant.button
              )}
              aria-expanded={isOpen}
            >
              <span className="text-lg font-semibold text-gray-900 pr-4">
                {item.question}
              </span>
              <svg
                className={cn(
                  'w-5 h-5 text-gray-500 transition-transform duration-200 flex-shrink-0',
                  isOpen && 'transform rotate-180'
                )}
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
            
            <div
              className={cn(
                'overflow-hidden transition-all duration-300 ease-in-out',
                isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
              )}
            >
              <div className={cn(
                'text-gray-600 leading-relaxed',
                currentVariant.content
              )}>
                {item.answer}
              </div>
            </div>
          </FadeInSection>
        );
      })}
    </div>
  );
};

export default AccordionFAQ;
