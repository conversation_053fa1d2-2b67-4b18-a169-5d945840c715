import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface IntegrationService {
  id: string;
  name: string;
  icon: string;
  bgColor: string;
  position: { top: string; left: string };
  angle: number;
}

interface IntegrationDiagramProps {
  className?: string;
  animated?: boolean;
}

const IntegrationDiagram: React.FC<IntegrationDiagramProps> = ({
  className,
  animated = true
}) => {
  const services = [
    { id: 'google-workspace', name: 'Google Workspace', icon: '📊', bgColor: 'bg-green-500' },
    { id: 'telegram', name: 'Telegram', icon: '💬', bgColor: 'bg-blue-500' },
    { id: 'whatsapp', name: 'WhatsApp', icon: '📱', bgColor: 'bg-green-600' },
    { id: 'payments', name: 'Payments', icon: '💳', bgColor: 'bg-yellow-500' },
    { id: 'website', name: 'Website', icon: '#️⃣', bgColor: 'bg-purple-500' },
    { id: 'documents', name: 'Documents', icon: '📄', bgColor: 'bg-gray-500' },
    { id: 'analytics', name: 'Analytics', icon: '📈', bgColor: 'bg-blue-600' },
    { id: 'email', name: 'Email', icon: '📧', bgColor: 'bg-red-500' }
  ];

  // Use fixed positions that we know work
  const integrationServices: IntegrationService[] = [
    { ...services[0], position: { top: '15%', left: '50%' }, angle: 0 },      // Google Workspace - top
    { ...services[1], position: { top: '25%', left: '75%' }, angle: 45 },     // Telegram - top-right  
    { ...services[2], position: { top: '50%', left: '85%' }, angle: 90 },     // WhatsApp - right
    { ...services[3], position: { top: '75%', left: '75%' }, angle: 135 },    // Payments - bottom-right
    { ...services[4], position: { top: '85%', left: '50%' }, angle: 180 },    // Website - bottom
    { ...services[5], position: { top: '75%', left: '25%' }, angle: 225 },    // Documents - bottom-left
    { ...services[6], position: { top: '50%', left: '15%' }, angle: 270 },    // Analytics - left
    { ...services[7], position: { top: '25%', left: '25%' }, angle: 315 }     // Email - top-left
  ];

  return (
    <FadeInSection className={cn('relative w-full', className)}>
      <div className="relative w-full max-w-4xl mx-auto h-[500px] md:h-[600px] bg-gradient-to-br from-slate-50 to-blue-50 rounded-xl border border-gray-200 shadow-lg overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: 'radial-gradient(circle at 2px 2px, rgba(59, 130, 246, 0.4) 1px, transparent 0)',
            backgroundSize: '30px 30px'
          }} />
        </div>

        {/* SVG for connection lines */}
        <svg
          className="absolute inset-0 w-full h-full pointer-events-none z-10"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
        >
          {integrationServices.map((service, index) => (
            <line
              key={service.id}
              x1={parseFloat(service.position.left)}
              y1={parseFloat(service.position.top)}
              x2="50"
              y2="50"
              stroke="#CBD5E0"
              strokeWidth="0.5"
              strokeDasharray="2 2"
              className={animated ? 'animate-pulse' : ''}
              style={{ 
                animationDelay: `${index * 0.2}s`,
                opacity: 0.7
              }}
            />
          ))}
        </svg>

        {/* Service nodes positioned around the circle */}
        {integrationServices.map((service, index) => (
          <div
            key={service.id}
            className="absolute z-20"
            style={{
              top: service.position.top,
              left: service.position.left,
              transform: 'translate(-50%, -50%)'
            }}
          >
            <FadeInSection delay={200 + index * 150}>
            <div className="flex flex-col items-center group cursor-pointer">
              <div className={`${service.bgColor} w-14 h-14 md:w-16 md:h-16 rounded-full flex items-center justify-center shadow-xl hover:scale-110 transition-all duration-300 border-3 border-white`}>
                <span className="text-lg md:text-xl text-white">{service.icon}</span>
              </div>
              <div className="mt-2 text-xs md:text-sm font-semibold text-gray-700 text-center max-w-16 leading-tight">
                {service.name}
              </div>
              {/* Animated pulse ring */}
              {animated && (
                <div 
                  className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 md:w-18 md:h-18 border-2 border-blue-300 rounded-full animate-ping opacity-20"
                  style={{ animationDelay: `${index * 0.3}s` }}
                />
              )}
            </div>
            </FadeInSection>
          </div>
        ))}

        {/* Central Kommo CRM */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30">
          <FadeInSection delay={800}>
          <div className="relative">
            <div className="w-20 h-20 md:w-24 md:h-24 bg-gradient-to-br from-blue-600 to-blue-700 rounded-full flex flex-col items-center justify-center text-white shadow-2xl border-4 border-white">
              <div className="text-lg md:text-xl mb-1">🎯</div>
              <div className="font-bold text-xs md:text-sm text-center leading-tight">
                Kommo CRM
              </div>
            </div>
            
            {/* Central pulse animation */}
            {animated && (
              <>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 md:w-28 md:h-28 border-2 border-blue-400 rounded-full animate-ping opacity-30" />
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-28 h-28 md:w-32 md:h-32 border-2 border-blue-300 rounded-full animate-ping opacity-20" style={{ animationDelay: '0.5s' }} />
              </>
            )}
          </div>
          </FadeInSection>
        </div>


      </div>

      {/* Integration benefits below the diagram */}
      <FadeInSection delay={1600} className="mt-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
          <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200 text-center">
            <div className="text-3xl mb-3">⚡</div>
            <h4 className="font-bold text-gray-900 mb-2">Real-time Sync</h4>
            <p className="text-sm text-gray-600">Data flows instantly between all systems. No delays, no manual updates.</p>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200 text-center">
            <div className="text-3xl mb-3">🔄</div>
            <h4 className="font-bold text-gray-900 mb-2">Automated Workflows</h4>
            <p className="text-sm text-gray-600">Triggers and actions happen automatically. Focus on selling, not data entry.</p>
          </div>
          <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200 text-center">
            <div className="text-3xl mb-3">📊</div>
            <h4 className="font-bold text-gray-900 mb-2">Complete Visibility</h4>
            <p className="text-sm text-gray-600">See your entire business from one dashboard. Make informed decisions.</p>
          </div>
        </div>
      </FadeInSection>


    </FadeInSection>
  );
};

export default IntegrationDiagram;
