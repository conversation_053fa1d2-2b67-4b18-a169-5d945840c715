'use client';

import React, { useState } from 'react';
import Button from './Button';
import { cn } from '@/lib/utils';

interface QuizStep {
  id: string;
  question: string;
  options: {
    id: string;
    text: string;
    value: string;
  }[];
}

interface QuizFormProps {
  className?: string;
  onComplete?: (answers: Record<string, string>) => void;
}

const quizSteps: QuizStep[] = [
  {
    id: 'business-type',
    question: 'What type of business do you have?',
    options: [
      { id: 'b2b', text: 'B2B Sales', value: 'b2b' },
      { id: 'real-estate', text: 'Real Estate', value: 'real-estate' },
      { id: 'saas', text: 'SaaS/Tech', value: 'saas' },
      { id: 'healthcare', text: 'Healthcare', value: 'healthcare' },
      { id: 'other', text: 'Other', value: 'other' }
    ]
  },
  {
    id: 'team-size',
    question: 'How many people are in your sales team?',
    options: [
      { id: 'solo', text: 'Just me', value: '1' },
      { id: 'small', text: '2-5 people', value: '2-5' },
      { id: 'medium', text: '6-15 people', value: '6-15' },
      { id: 'large', text: '16+ people', value: '16+' }
    ]
  },
  {
    id: 'current-crm',
    question: 'Do you currently use a CRM system?',
    options: [
      { id: 'no-crm', text: 'No CRM system', value: 'none' },
      { id: 'kommo', text: 'Already using Kommo', value: 'kommo' },
      { id: 'other-crm', text: 'Using another CRM', value: 'other' },
      { id: 'spreadsheets', text: 'Using spreadsheets', value: 'spreadsheets' }
    ]
  }
];

const QuizForm: React.FC<QuizFormProps> = ({ className, onComplete }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [isCompleted, setIsCompleted] = useState(false);

  const handleAnswer = (stepId: string, value: string) => {
    const newAnswers = { ...answers, [stepId]: value };
    setAnswers(newAnswers);

    if (currentStep < quizSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      setIsCompleted(true);
      onComplete?.(newAnswers);
    }
  };

  const goBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const resetQuiz = () => {
    setCurrentStep(0);
    setAnswers({});
    setIsCompleted(false);
  };

  const getPersonalizedResult = () => {
    const businessType = answers['business-type'];
    const teamSize = answers['team-size'];
    const currentCrm = answers['current-crm'];

    const title = "Perfect! Here&apos;s what we recommend for you:";
    const recommendations = [];

    if (currentCrm === 'none') {
      recommendations.push("🚀 Kommo CRM Implementation - Start with a properly configured system");
    } else if (currentCrm === 'kommo') {
      recommendations.push("⚡ Kommo CRM Optimization - Maximize your existing investment");
    } else {
      recommendations.push("🔄 Migration to Kommo CRM - Seamless transition from your current system");
    }

    if (teamSize === '6-15' || teamSize === '16+') {
      recommendations.push("🎯 Advanced automation and reporting for larger teams");
    }

    if (businessType === 'real-estate') {
      recommendations.push("🏠 Real estate specific integrations and workflows");
    } else if (businessType === 'saas') {
      recommendations.push("💻 SaaS metrics tracking and customer lifecycle automation");
    }

    recommendations.push("🔗 Make.com integrations for complete automation");

    return { title, recommendations };
  };

  if (isCompleted) {
    const result = getPersonalizedResult();
    
    return (
      <div className={cn("bg-white rounded-lg p-6 max-w-md mx-auto", className)}>
        <div className="p-4">
          <div className="text-center mb-6">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
            <svg className="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">{result.title}</h3>
        </div>

        <div className="space-y-3 mb-6">
          {result.recommendations.map((rec, index) => (
            <div key={index} className="flex items-start space-x-2">
              <span className="text-sm text-gray-700">{rec}</span>
            </div>
          ))}
        </div>

        <div className="space-y-3">
          <Button variant="primary" size="lg" className="w-full">
            Get Personalized Consultation
          </Button>
          <Button variant="ghost" size="sm" className="w-full" onClick={resetQuiz}>
            Take Quiz Again
          </Button>
        </div>
        </div>
      </div>
    );
  }

  const step = quizSteps[currentStep];
  const progress = ((currentStep + 1) / quizSteps.length) * 100;

  return (
    <div className={cn("bg-white rounded-lg p-6 max-w-md mx-auto", className)}>
      <div className="p-4">
        {/* Progress Bar */}
        <div className="mb-6">
        <div className="flex justify-between text-sm text-gray-600 mb-2">
          <span>Step {currentStep + 1} of {quizSteps.length}</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Question */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{step.question}</h3>
        <div className="space-y-3">
          {step.options.map((option) => (
            <button
              key={option.id}
              onClick={() => handleAnswer(step.id, option.value)}
              className="w-full text-left p-4 border border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
            >
              {option.text}
            </button>
          ))}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="ghost"
          size="sm"
          onClick={goBack}
          disabled={currentStep === 0}
        >
          Back
        </Button>
        <div className="text-sm text-gray-500">
          Click an option to continue
        </div>
      </div>
      </div>
    </div>
  );
};

export default QuizForm;
