import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Card from './Card';

interface TeamMember {
  name: string;
  position: string;
  responsibility: string;
  experience: string;
  avatar: string;
  expertise: string[];
}

const teamMembers: TeamMember[] = [
  {
    name: "<PERSON>",
    position: "CRM Implementation Director",
    responsibility: "Strategic planning and complex CRM implementations",
    experience: "8+ years in CRM consulting",
    avatar: "👨‍💼",
    expertise: ["Kommo CRM Expert", "Business Process Analysis", "Team Leadership"]
  },
  {
    name: "<PERSON>",
    position: "Automation Specialist",
    responsibility: "Make.com integrations and workflow automation",
    experience: "6+ years in automation",
    avatar: "👩‍💻",
    expertise: ["Make.com Certified", "API Integrations", "Process Automation"]
  },
  {
    name: "<PERSON>",
    position: "Technical Lead",
    responsibility: "Custom integrations and technical solutions",
    experience: "10+ years in software development",
    avatar: "👨‍💻",
    expertise: ["Full-Stack Development", "System Architecture", "Database Design"]
  },
  {
    name: "<PERSON>",
    position: "Client Success Manager",
    responsibility: "Training, support, and client relationship management",
    experience: "5+ years in customer success",
    avatar: "👩‍💼",
    expertise: ["Training & Education", "Customer Success", "Project Management"]
  }
];

const TeamSection: React.FC = () => {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold">
              👥 Our Team
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Meet the People Behind Your Success
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our experienced team combines deep technical expertise with genuine care for your business results.
              Each member brings unique skills and proven experience in CRM implementation and automation.
            </p>
          </div>
        </FadeInSection>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <FadeInSection key={index} delay={200 + index * 150}>
              <Card variant="elevated" className="p-6 text-center h-full hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                {/* Avatar */}
                <div className="relative mb-6">
                  <div className="w-24 h-24 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-4xl text-white shadow-lg">
                    {member.avatar}
                  </div>
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm">
                    ✓
                  </div>
                </div>

                {/* Info */}
                <div className="space-y-3">
                  <h3 className="text-lg font-bold text-gray-900">{member.name}</h3>
                  <p className="text-blue-600 font-semibold text-sm">{member.position}</p>
                  
                  <div className="text-gray-600 text-sm">
                    <p className="mb-2">{member.responsibility}</p>
                    <p className="text-xs text-gray-500 italic">{member.experience}</p>
                  </div>

                  {/* Expertise Tags */}
                  <div className="flex flex-wrap gap-1 justify-center mt-4">
                    {member.expertise.map((skill, skillIndex) => (
                      <span
                        key={skillIndex}
                        className="inline-block bg-blue-50 text-blue-700 text-xs px-2 py-1 rounded-full"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </Card>
            </FadeInSection>
          ))}
        </div>

        {/* Team Values */}
        <FadeInSection delay={800}>
          <div className="mt-16 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                What Unites Our Team
              </h3>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Beyond technical expertise, we share common values that drive our approach to every project
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mx-auto mb-3">
                  🎯
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Results-Focused</h4>
                <p className="text-gray-600 text-sm">
                  Every decision we make is guided by measurable business outcomes for our clients
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mx-auto mb-3">
                  🤝
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Partnership Mindset</h4>
                <p className="text-gray-600 text-sm">
                  We see ourselves as an extension of your team, invested in your long-term success
                </p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 text-xl mx-auto mb-3">
                  📚
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Continuous Learning</h4>
                <p className="text-gray-600 text-sm">
                  We stay ahead of industry trends to bring you the most effective solutions
                </p>
              </div>
            </div>
          </div>
        </FadeInSection>

        {/* Contact Team CTA */}
        <FadeInSection delay={1000}>
          <div className="mt-12 text-center">
            <div className="bg-white border border-gray-200 rounded-xl p-6 max-w-2xl mx-auto shadow-sm">
              <h4 className="text-lg font-semibold text-gray-900 mb-2">
                Want to Meet the Team?
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                Schedule a call to discuss your project with our experts personally
              </p>
              <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm font-medium">
                Schedule a Call
              </button>
            </div>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default TeamSection;
