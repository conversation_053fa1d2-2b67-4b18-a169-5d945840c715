import React from 'react';

interface PricingSchemaProps {
  className?: string;
}

const PricingSchema: React.FC<PricingSchemaProps> = ({ className = '' }) => {
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Kommo CRM Implementation",
    "description": "Professional Kommo CRM implementation services with transparent pricing and guaranteed results",
    "provider": {
      "@type": "Organization",
      "name": "Setmee",
      "url": "https://setmee.com",
      "logo": "https://setmee.com/logo.png"
    },
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "CRM Implementation Plans",
      "itemListElement": [
        {
          "@type": "Offer",
          "name": "Basic Plan",
          "description": "Perfect for small businesses and startups beginning their automation journey",
          "price": "1200",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "validFrom": "2024-01-01",
          "category": "CRM Implementation",
          "itemOffered": {
            "@type": "Service",
            "name": "Basic CRM Implementation",
            "description": "Current process audit, standard sales funnel setup, integration of up to 2 channels, custom fields and basic automation, import up to 2,000 contacts/leads, training for up to 5 users, 30 days post-launch support"
          }
        },
        {
          "@type": "Offer",
          "name": "Standard Plan",
          "description": "Ideal for growing teams with multiple sales channels and advanced automation needs",
          "price": "3000",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "validFrom": "2024-01-01",
          "category": "CRM Implementation",
          "itemOffered": {
            "@type": "Service",
            "name": "Standard CRM Implementation",
            "description": "Deep business process audit and CRM concept development, custom funnel setup with advanced stages, service integrations, advanced automation, data migration from legacy systems, training for up to 15 users, 30 days priority support"
          }
        },
        {
          "@type": "Offer",
          "name": "Enterprise Plan",
          "description": "Comprehensive solution for medium and large businesses with complex processes",
          "price": "6000",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "validFrom": "2024-01-01",
          "category": "CRM Implementation",
          "itemOffered": {
            "@type": "Service",
            "name": "Enterprise CRM Implementation",
            "description": "Complete process audit, CRM architecture with multi-level funnels, unlimited integrations, custom widgets and automation development, data migration of any volume, unlimited user training, 60 days premium support, quarterly audit and optimization"
          }
        }
      ]
    },
    "areaServed": "Worldwide",
    "serviceType": "CRM Implementation",
    "keywords": "Kommo CRM implementation, CRM setup pricing, CRM automation for business, Kommo partner, sales automation"
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
      className={className}
    />
  );
};

export default PricingSchema;
