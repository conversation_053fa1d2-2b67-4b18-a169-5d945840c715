import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Card from './Card';

interface HistoryMilestone {
  year: string;
  title: string;
  description: string;
  achievement: string;
  icon: string;
}

const milestones: HistoryMilestone[] = [
  {
    year: "2015",
    title: "Foundation & Kommo Partnership",
    description: "Started as a CRM consulting team and immediately became an official Kommo CRM partner",
    achievement: "Certified Kommo partner status achieved",
    icon: "🌱"
  },
  {
    year: "2017",
    title: "Expertise Development",
    description: "Deepened our CRM implementation expertise and developed proven methodologies",
    achievement: "50+ successful CRM implementations",
    icon: "🎯"
  },
  {
    year: "2019",
    title: "Automation Expansion",
    description: "Expanded into advanced automation with Make.com integrations and custom solutions",
    achievement: "100+ automation projects completed",
    icon: "⚙️"
  },
  {
    year: "2021",
    title: "Scale & Recognition",
    description: "Reached significant milestones in project delivery and client satisfaction",
    achievement: "200+ projects, 2000+ trained managers",
    icon: "📈"
  },
  {
    year: "2025",
    title: "Industry Leadership",
    description: "Celebrating 10 years as a leading CRM implementation partner with proven methodology",
    achievement: "320+ projects, 4000+ trained managers",
    icon: "🏆"
  }
];

const coreValues = [
  {
    title: "Transparency",
    description: "We believe in open communication, clear processes, and honest feedback at every stage of our partnership.",
    icon: "🔍",
    color: "blue"
  },
  {
    title: "Excellence",
    description: "We strive for the highest quality in everything we do, from initial consultation to ongoing support.",
    icon: "⭐",
    color: "purple"
  },
  {
    title: "Partnership",
    description: "We see ourselves as an extension of your team, committed to your long-term success and growth.",
    icon: "🤝",
    color: "green"
  },
  {
    title: "Innovation",
    description: "We continuously explore new technologies and methodologies to bring you cutting-edge solutions.",
    icon: "💡",
    color: "orange"
  }
];

const CompanyHistory: React.FC = () => {
  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'bg-blue-50 border-blue-200 text-blue-700',
      purple: 'bg-purple-50 border-purple-200 text-purple-700',
      green: 'bg-green-50 border-green-200 text-green-700',
      orange: 'bg-orange-50 border-orange-200 text-orange-700'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* History Section */}
        <FadeInSection>
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-gray-100 text-gray-700 rounded-full text-sm font-semibold">
              📚 Our Journey
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              A Decade of Growth and Innovation
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              From a small consulting team to a recognized industry leader, our journey reflects 
              our commitment to helping businesses transform through technology.
            </p>
          </div>
        </FadeInSection>

        {/* Timeline */}
        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-blue-200 via-purple-200 to-green-200 hidden lg:block"></div>

          <div className="space-y-12">
            {milestones.map((milestone, index) => (
              <FadeInSection key={index} delay={200 + index * 150}>
                <div className={cn(
                  "flex items-center",
                  index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"
                )}>
                  {/* Content */}
                  <div className="w-full lg:w-5/12">
                    <Card variant="elevated" className="p-6 hover:shadow-xl transition-all duration-300">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xl flex-shrink-0">
                          {milestone.icon}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <span className="bg-blue-100 text-blue-800 text-sm font-bold px-3 py-1 rounded-full mr-3">
                              {milestone.year}
                            </span>
                          </div>
                          <h3 className="text-lg font-bold text-gray-900 mb-2">{milestone.title}</h3>
                          <p className="text-gray-600 mb-3">{milestone.description}</p>
                          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                            <p className="text-green-700 text-sm font-medium">
                              ✓ {milestone.achievement}
                            </p>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </div>

                  {/* Timeline dot */}
                  <div className="hidden lg:flex w-2/12 justify-center">
                    <div className="w-4 h-4 bg-blue-600 rounded-full border-4 border-white shadow-lg"></div>
                  </div>

                  {/* Spacer */}
                  <div className="hidden lg:block w-5/12"></div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>

        {/* Values Section */}
        <FadeInSection delay={1000}>
          <div className="mt-20">
            <div className="text-center mb-12">
              <div className="inline-block px-4 py-2 mb-6 bg-purple-100 text-purple-700 rounded-full text-sm font-semibold">
                💎 Our Values
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
                Principles That Guide Everything We Do
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                These core values shape our culture, drive our decisions, and define how we work with our clients.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {coreValues.map((value, index) => (
                <FadeInSection key={index} delay={1200 + index * 150}>
                  <Card variant="outlined" className={cn(
                    "p-6 text-center h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1 border-2",
                    getColorClasses(value.color)
                  )}>
                    <div className="text-3xl mb-4">{value.icon}</div>
                    <h4 className="text-lg font-bold text-gray-900 mb-3">{value.title}</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">{value.description}</p>
                  </Card>
                </FadeInSection>
              ))}
            </div>
          </div>
        </FadeInSection>

        {/* Company Photo Placeholder */}
        <FadeInSection delay={1600}>
          <div className="mt-16 bg-white rounded-2xl p-8 shadow-lg">
            <div className="text-center">
              <div className="w-full h-64 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center mb-6">
                <div className="text-center text-gray-600">
                  <div className="text-6xl mb-4">🏢</div>
                  <h4 className="text-lg font-semibold">Our Office & Team</h4>
                  <p className="text-sm">Where innovation meets collaboration</p>
                </div>
              </div>
              <div className="max-w-2xl mx-auto">
                <h4 className="text-xl font-bold text-gray-900 mb-3">
                  Building the Future of Business Automation
                </h4>
                <p className="text-gray-600">
                  Our modern workspace in the heart of the tech district reflects our commitment to innovation 
                  and collaboration. Here, our team of experts works together to create solutions that transform 
                  businesses and drive growth for our clients worldwide.
                </p>
              </div>
            </div>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default CompanyHistory;
