'use client';

import { useEffect, useState } from 'react';

interface NumberFormatterProps {
  value: number;
  prefix?: string;
  suffix?: string;
  className?: string;
}

const NumberFormatter: React.FC<NumberFormatterProps> = ({ 
  value, 
  prefix = '', 
  suffix = '', 
  className = '' 
}) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Use consistent formatting that works on both server and client
  const formatNumber = (num: number): string => {
    // Force English locale formatting to avoid hydration mismatch
    return new Intl.NumberFormat('en-US').format(num);
  };

  if (!isClient) {
    // Server-side rendering: use simple formatting
    return (
      <span className={className}>
        {prefix}{formatNumber(value)}{suffix}
      </span>
    );
  }

  // Client-side rendering: use the same formatting
  return (
    <span className={className}>
      {prefix}{formatNumber(value)}{suffix}
    </span>
  );
};

export default NumberFormatter;
