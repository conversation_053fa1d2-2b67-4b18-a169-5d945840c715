'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRoiStore } from '@/store/roiStore';
import { CASE_STUDIES, CaseStudy } from '@/data/roiCalculatorData';
import Button from './Button';
import NumberFormatter from './NumberFormatter';

const CaseStudiesSlider: React.FC = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { setFormData, calculateResults, formData } = useRoiStore();

  const currentCase = CASE_STUDIES[currentIndex];

  const nextCase = () => {
    setCurrentIndex((prev) => (prev + 1) % CASE_STUDIES.length);
  };

  const prevCase = () => {
    setCurrentIndex((prev) => (prev - 1 + CASE_STUDIES.length) % CASE_STUDIES.length);
  };

  const applyCaseData = (caseStudy: CaseStudy) => {
    setFormData({
      leads: caseStudy.before.leads,
      conversion: caseStudy.before.conversion,
      avgDealSize: caseStudy.before.revenue / ((caseStudy.before.leads * caseStudy.before.conversion) / 100),
      timePerLead: caseStudy.before.timePerLead,
      niche: caseStudy.niche,
    });
    
    // Scroll to calculator and calculate
    setTimeout(() => {
      calculateResults();
      const formElement = document.getElementById('roi-form');
      if (formElement) {
        formElement.scrollIntoView({ behavior: 'smooth' });
      }
    }, 100);
  };

  const findSimilarCase = () => {
    if (!formData.niche && !formData.leads) return null;
    
    return CASE_STUDIES.find(caseStudy => {
      const nicheMatch = formData.niche === caseStudy.niche;
      const leadsMatch = Math.abs(formData.leads - caseStudy.before.leads) < 100;
      return nicheMatch || leadsMatch;
    });
  };

  const similarCase = findSimilarCase();

  return (
    <div className="bg-gray-50 py-16">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Real Success Stories
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See how businesses like yours transformed their operations with automation
          </p>
        </div>

        {/* Similar Case Highlight */}
        {similarCase && (
          <motion.div
            className="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col md:flex-row items-center justify-between">
              <div className="mb-4 md:mb-0">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">
                  📊 Similar Business Found!
                </h3>
                <p className="text-blue-700">
                  {similarCase.title} - {similarCase.niche}
                </p>
              </div>
              <Button
                onClick={() => applyCaseData(similarCase)}
                variant="primary"
                size="md"
              >
                Use This Case Data
              </Button>
            </div>
          </motion.div>
        )}

        {/* Case Studies Slider */}
        <div className="relative">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndex}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -100 }}
              transition={{ duration: 0.5 }}
              className="bg-white rounded-2xl shadow-xl overflow-hidden"
            >
              <div className="p-8">
                <div className="grid md:grid-cols-2 gap-8">
                  {/* Before/After Stats */}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">
                      {currentCase.title}
                    </h3>
                    
                    <div className="space-y-6">
                      {/* Before */}
                      <div className="bg-red-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-red-800 mb-3">📉 Before Automation</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Leads/month:</span>
                            <div className="font-semibold">{currentCase.before.leads}</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Conversion:</span>
                            <div className="font-semibold">{currentCase.before.conversion}%</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Revenue:</span>
                            <div className="font-semibold">
                              <NumberFormatter value={currentCase.before.revenue} prefix="$" />
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-600">Time/lead:</span>
                            <div className="font-semibold">{currentCase.before.timePerLead}min</div>
                          </div>
                        </div>
                      </div>

                      {/* After */}
                      <div className="bg-green-50 p-4 rounded-lg">
                        <h4 className="font-semibold text-green-800 mb-3">📈 After Automation</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Conversion:</span>
                            <div className="font-semibold text-green-600">{currentCase.after.conversion}%</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Revenue:</span>
                            <div className="font-semibold text-green-600">
                              <NumberFormatter value={currentCase.after.revenue} prefix="$" />
                            </div>
                          </div>
                          <div>
                            <span className="text-gray-600">Saved hours:</span>
                            <div className="font-semibold text-green-600">{currentCase.after.savedHours}h/month</div>
                          </div>
                          <div>
                            <span className="text-gray-600">Revenue increase:</span>
                            <div className="font-semibold text-green-600">
                              <NumberFormatter
                                value={currentCase.after.revenue - currentCase.before.revenue}
                                prefix="+$"
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Quote and Actions */}
                  <div className="flex flex-col justify-between">
                    <div>
                      <blockquote className="text-lg text-gray-700 italic mb-6">
                        "                        {currentCase.quote}"
                      </blockquote>
                      <div className="text-sm text-gray-600">
                        <div className="font-semibold">{currentCase.author}</div>
                        <div>{currentCase.position}</div>
                      </div>
                    </div>

                    <div className="mt-8 space-y-4">
                      <Button
                        onClick={() => applyCaseData(currentCase)}
                        variant="primary"
                        size="lg"
                        className="w-full"
                      >
                        Calculate Based on This Case
                      </Button>
                      <p className="text-xs text-gray-500 text-center">
                        This will populate the calculator with similar business metrics
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex justify-between items-center mt-6">
            <button
              onClick={prevCase}
              className="p-3 rounded-full bg-white shadow-lg hover:shadow-xl transition-shadow"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <div className="flex space-x-2">
              {CASE_STUDIES.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentIndex(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentIndex ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>

            <button
              onClick={nextCase}
              className="p-3 rounded-full bg-white shadow-lg hover:shadow-xl transition-shadow"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CaseStudiesSlider;
