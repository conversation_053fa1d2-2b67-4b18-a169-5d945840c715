'use client';

import { useEffect } from 'react';
import { saveUTMToSession } from '@/utils/utmUtils';

const UTMTracker: React.FC = () => {
  useEffect(() => {
    // Save UTM parameters on page load
    saveUTMToSession();
    
    // Handle anchor links on page load
    if (typeof window !== 'undefined' && window.location.hash === '#results') {
      setTimeout(() => {
        const resultsElement = document.getElementById('roi-results');
        if (resultsElement) {
          resultsElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 1000); // Wait for page to fully load
    }
  }, []);

  return null; // This component doesn't render anything
};

export default UTMTracker;
