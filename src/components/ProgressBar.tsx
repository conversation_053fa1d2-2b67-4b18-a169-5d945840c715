'use client';

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface ProgressBarProps {
  label: string;
  value: number; // 0-100
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red';
  size?: 'sm' | 'md' | 'lg';
  animated?: boolean;
  delay?: number;
  showValue?: boolean;
  className?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  label,
  value,
  color = 'blue',
  size = 'md',
  animated = true,
  delay = 0,
  showValue = true,
  className
}) => {
  const [animatedValue, setAnimatedValue] = useState(0);

  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setAnimatedValue(value);
      }, delay);
      return () => clearTimeout(timer);
    } else {
      setAnimatedValue(value);
    }
  }, [value, animated, delay]);

  const colorClasses = {
    blue: {
      bg: 'bg-blue-500',
      bgLight: 'bg-blue-100',
      text: 'text-blue-600'
    },
    green: {
      bg: 'bg-green-500',
      bgLight: 'bg-green-100',
      text: 'text-green-600'
    },
    purple: {
      bg: 'bg-purple-500',
      bgLight: 'bg-purple-100',
      text: 'text-purple-600'
    },
    orange: {
      bg: 'bg-orange-500',
      bgLight: 'bg-orange-100',
      text: 'text-orange-600'
    },
    red: {
      bg: 'bg-red-500',
      bgLight: 'bg-red-100',
      text: 'text-red-600'
    }
  };

  const sizeClasses = {
    sm: {
      height: 'h-2',
      text: 'text-sm',
      spacing: 'mb-2'
    },
    md: {
      height: 'h-3',
      text: 'text-base',
      spacing: 'mb-3'
    },
    lg: {
      height: 'h-4',
      text: 'text-lg',
      spacing: 'mb-4'
    }
  };

  const currentColor = colorClasses[color];
  const currentSize = sizeClasses[size];

  const ProgressContent = () => (
    <div className={cn('w-full', className)}>
      <div className={cn(
        'flex justify-between items-center',
        currentSize.spacing
      )}>
        <span className={cn(
          'font-medium text-gray-700',
          currentSize.text
        )}>
          {label}
        </span>
        {showValue && (
          <span className={cn(
            'font-bold',
            currentSize.text,
            currentColor.text
          )}>
            {Math.round(animatedValue)}%
          </span>
        )}
      </div>
      
      <div className={cn(
        'w-full rounded-full overflow-hidden',
        currentSize.height,
        currentColor.bgLight
      )}>
        <div
          className={cn(
            'h-full rounded-full transition-all duration-1000 ease-out',
            currentColor.bg
          )}
          style={{ width: `${animatedValue}%` }}
        />
      </div>
    </div>
  );

  if (animated) {
    return (
      <FadeInSection delay={delay}>
        <ProgressContent />
      </FadeInSection>
    );
  }

  return <ProgressContent />;
};

export default ProgressBar;
