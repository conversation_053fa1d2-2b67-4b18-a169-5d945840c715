import React from 'react';

interface StructuredDataProps {
  type: 'organization' | 'service' | 'breadcrumb';
  data?: Record<string, unknown>;
}

const StructuredData: React.FC<StructuredDataProps> = ({ type, data }) => {
  const getStructuredData = () => {
    switch (type) {
      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "Setmee",
          "description": "Certified Kommo CRM Partner specializing in implementation, optimization, and integrations",
          "url": "https://setmee.com",
          "logo": "https://setmee.com/setmee-logo.svg",
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+1-XXX-XXX-XXXX",
            "contactType": "customer service",
            "email": "<EMAIL>",
            "availableLanguage": ["English", "Russian"]
          },
          "address": {
            "@type": "PostalAddress",
            "addressCountry": "US"
          },
          "sameAs": [
            "https://t.me/setmee",
            "https://wa.me/setmee"
          ],
          "foundingDate": "2014",
          "numberOfEmployees": "10-50",
          "knowsAbout": [
            "Kommo CRM",
            "Sales Automation",
            "CRM Implementation",
            "Make.com Integrations",
            "Business Process Automation"
          ]
        };

      case 'service':
        return {
          "@context": "https://schema.org",
          "@type": "Service",
          "name": "Kommo CRM Implementation",
          "description": "Professional Kommo CRM implementation and optimization services",
          "provider": {
            "@type": "Organization",
            "name": "SetMee"
          },
          "serviceType": "CRM Implementation",
          "areaServed": "Worldwide",
          "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "CRM Services",
            "itemListElement": [
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Kommo CRM Implementation"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Kommo CRM Optimization"
                }
              },
              {
                "@type": "Offer",
                "itemOffered": {
                  "@type": "Service",
                  "name": "Make.com Integrations"
                }
              }
            ]
          }
        };

      default:
        return data;
    }
  };

  const structuredData = getStructuredData();

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
};

export default StructuredData;
