import React from 'react';
import { cn } from '@/lib/utils';

interface FeatureCardProps {
  icon?: string | React.ReactNode;
  title: string;
  description: string;
  className?: string;
  variant?: 'default' | 'outlined' | 'elevated' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  hover?: boolean;
  borderColor?: 'blue' | 'green' | 'purple' | 'red' | 'gray';
  iconColor?: string;
  onClick?: () => void;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  icon,
  title,
  description,
  className,
  variant = 'outlined',
  size = 'md',
  hover = true,
  borderColor = 'blue',
  iconColor,
  onClick
}) => {
  const baseClasses = 'rounded-lg transition-all duration-300';
  
  const variants = {
    default: 'bg-white',
    outlined: 'bg-white border border-gray-200',
    elevated: 'bg-white shadow-lg',
    gradient: 'bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-100'
  };

  const sizes = {
    sm: {
      padding: 'p-4',
      iconSize: 'text-2xl',
      titleSize: 'text-base font-semibold',
      descriptionSize: 'text-sm'
    },
    md: {
      padding: 'p-6',
      iconSize: 'text-3xl',
      titleSize: 'text-lg font-semibold',
      descriptionSize: 'text-base'
    },
    lg: {
      padding: 'p-8',
      iconSize: 'text-4xl',
      titleSize: 'text-xl font-semibold',
      descriptionSize: 'text-lg'
    }
  };

  const borderColors = {
    blue: 'border-l-4 border-l-blue-500',
    green: 'border-l-4 border-l-green-500',
    purple: 'border-l-4 border-l-purple-500',
    red: 'border-l-4 border-l-red-500',
    gray: 'border-l-4 border-l-gray-500'
  };

  const hoverClasses = hover ? 'hover:shadow-xl hover:-translate-y-1 cursor-pointer' : '';
  const clickableClasses = onClick ? 'cursor-pointer' : '';

  return (
    <div
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size].padding,
        variant === 'outlined' && borderColors[borderColor],
        hoverClasses,
        clickableClasses,
        className
      )}
      onClick={onClick}
    >
      {icon && (
        <div className={cn(
          'mb-4',
          sizes[size].iconSize,
          iconColor || 'text-blue-600'
        )}>
          {typeof icon === 'string' ? (
            <span>{icon}</span>
          ) : (
            icon
          )}
        </div>
      )}
      
      <h3 className={cn(
        'text-gray-900 mb-3',
        sizes[size].titleSize
      )}>
        {title}
      </h3>
      
      <p className={cn(
        'text-gray-600',
        sizes[size].descriptionSize
      )}>
        {description}
      </p>
    </div>
  );
};

export default FeatureCard;
