import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Card from './Card';
import Image from 'next/image';

interface Certificate {
  title: string;
  issuer: string;
  description: string;
  year: string;
  type: 'certification' | 'partnership' | 'achievement';
  icon: string;
  verified: boolean;
}



const certificates: Certificate[] = [
  {
    title: "Certified Kommo Partner",
    issuer: "Kommo CRM",
    description: "Official certification confirming our expertise in Kommo CRM implementation, customization, and optimization. This status is awarded only to partners who demonstrate exceptional knowledge and successful project delivery.",
    year: "2015",
    type: "certification",
    icon: "🏆",
    verified: true
  },
  {
    title: "Make.com Expert Partner",
    issuer: "Make.com",
    description: "Advanced certification in automation and integration solutions using Make.com platform. Demonstrates our ability to create complex workflows and connect multiple systems seamlessly.",
    year: "2023",
    type: "partnership",
    icon: "⚙️",
    verified: true
  },
  {
    title: "Business Process Excellence",
    issuer: "Industry Association",
    description: "Recognition for outstanding achievements in business process optimization and digital transformation consulting across multiple industries.",
    year: "2022",
    type: "achievement",
    icon: "📊",
    verified: true
  },
  {
    title: "Customer Success Excellence",
    issuer: "CRM Institute",
    description: "Award for maintaining the highest customer satisfaction rates and successful long-term partnerships in CRM implementation projects.",
    year: "2024",
    type: "achievement",
    icon: "⭐",
    verified: true
  }
];



const CertificatesSection: React.FC = () => {
  const getTypeColor = (type: string) => {
    const colorMap = {
      certification: 'bg-blue-50 border-blue-200 text-blue-700',
      partnership: 'bg-green-50 border-green-200 text-green-700',
      achievement: 'bg-purple-50 border-purple-200 text-purple-700'
    };
    return colorMap[type as keyof typeof colorMap] || colorMap.certification;
  };

  const getTypeLabel = (type: string) => {
    const labelMap = {
      certification: 'Certification',
      partnership: 'Partnership',
      achievement: 'Achievement'
    };
    return labelMap[type as keyof typeof labelMap] || 'Certification';
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Certificates Section */}
        <FadeInSection>
          <div className="text-center mb-16">
            <div className="inline-block px-4 py-2 mb-6 bg-yellow-100 text-yellow-700 rounded-full text-sm font-semibold">
              🏅 Certifications & Recognition
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Verified Expertise and Industry Recognition
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our certifications and achievements demonstrate our commitment to excellence and 
              provide you with confidence in our capabilities and professionalism.
            </p>
          </div>
        </FadeInSection>

        {/* Main Certificate Display */}
        <FadeInSection delay={200}>
          <div className="mb-16 flex justify-center">
            <div className="relative">
              <div className="w-80 h-64 bg-white rounded-xl shadow-2xl border border-gray-200 overflow-hidden">
                <Image
                  src="/Crtifikat.webp"
                  alt="Certified Kommo Partner Certificate"
                  fill
                  className="object-contain p-4"
                />
              </div>
              <div className="absolute -top-3 -right-3 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-2xl shadow-lg">
                ✓
              </div>
              <div className="absolute -bottom-3 -left-3 w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center text-white text-lg shadow-lg">
                ⭐
              </div>
            </div>
          </div>
        </FadeInSection>

        {/* Certificates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
          {certificates.map((cert, index) => (
            <FadeInSection key={index} delay={400 + index * 150}>
              <Card variant="elevated" className="p-6 h-full hover:shadow-xl transition-all duration-300">
                <div className="flex items-start space-x-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center text-2xl text-white flex-shrink-0">
                    {cert.icon}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <span className={cn(
                        "text-xs font-semibold px-3 py-1 rounded-full border",
                        getTypeColor(cert.type)
                      )}>
                        {getTypeLabel(cert.type)}
                      </span>
                      {cert.verified && (
                        <div className="flex items-center text-green-600 text-sm">
                          <span className="mr-1">✓</span>
                          Verified
                        </div>
                      )}
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{cert.title}</h3>
                    <p className="text-sm text-gray-600 mb-2">Issued by: <span className="font-medium">{cert.issuer}</span></p>
                    <p className="text-gray-600 text-sm mb-3">{cert.description}</p>
                    <div className="bg-gray-50 rounded-lg p-3">
                      <p className="text-gray-700 text-sm">
                        <span className="font-medium">Year:</span> {cert.year}
                      </p>
                    </div>
                  </div>
                </div>
              </Card>
            </FadeInSection>
          ))}
        </div>



        {/* Trust Statement */}
        <FadeInSection delay={1400}>
          <div className="mt-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
            <div className="text-center max-w-3xl mx-auto">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center text-2xl text-blue-600 mx-auto mb-6">
                🔒
              </div>
              <h4 className="text-xl font-bold text-gray-900 mb-4">
                Your Trust is Our Foundation
              </h4>
              <p className="text-gray-600 mb-6">
                These certifications and partnerships aren't just badges—they represent our ongoing commitment 
                to maintaining the highest standards of service, security, and expertise. When you work with us, 
                you're working with a team that's been vetted and approved by industry leaders.
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm">
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">ISO Compliant</span>
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">GDPR Certified</span>
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">SOC 2 Verified</span>
                <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full">Industry Approved</span>
              </div>
            </div>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default CertificatesSection;
