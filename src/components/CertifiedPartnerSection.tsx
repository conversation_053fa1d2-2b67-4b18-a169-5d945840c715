import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface CertifiedPartnerSectionProps {
  className?: string;
  variant?: 'blue' | 'white';
}

const CertifiedPartnerSection: React.FC<CertifiedPartnerSectionProps> = ({
  className,
  variant = 'blue'
}) => {
  const isBlue = variant === 'blue';
  
  return (
    <section className={cn(
      'py-20',
      isBlue ? 'text-white' : 'bg-white text-gray-900',
      className
    )}
    style={isBlue ? { backgroundColor: 'rgb(30, 64, 175)' } : undefined}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Certificate */}
          <FadeInSection>
            <div className="text-center lg:text-left">
              <div className="bg-white rounded-lg p-8 inline-block relative shadow-xl">
                {/* Header text */}
                <div className="text-center mb-4">
                  <div className="text-sm text-blue-600 font-medium mb-2">
                    CERTIFICATE OF PARTNERSHIP
                  </div>
                  <div className="text-lg font-semibold text-gray-900">
                    Who we are
                  </div>
                </div>

                <img
                  src="/Crtifikat.webp"
                  alt="Kommo Partnership Certificate"
                  className="w-full max-w-md mx-auto"
                />
              </div>
            </div>
          </FadeInSection>

          {/* Content */}
          <FadeInSection delay={200}>
            <div>
              <h2 className={cn(
                'text-3xl md:text-4xl font-bold mb-6',
                isBlue ? 'text-white' : 'text-gray-900'
              )}>
                Setmee — Certified Kommo Partner
              </h2>
              <p className={cn(
                'text-lg mb-8',
                isBlue ? 'text-blue-100' : 'text-gray-600'
              )}>
                We are a team of specialists specialising in workflow automation and Kommo-based sales. 
                We are committed to solving complex problems and interesting cases.
              </p>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-6 mb-8">
                <div>
                  <div className="text-3xl font-bold text-orange-400 mb-1">10 YEARS</div>
                  <div className={cn(
                    'text-sm',
                    isBlue ? 'text-blue-200' : 'text-gray-500'
                  )}>
                    CRM IMPLEMENTATION EXPERIENCE
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-orange-400 mb-1">320+</div>
                  <div className={cn(
                    'text-sm',
                    isBlue ? 'text-blue-200' : 'text-gray-500'
                  )}>
                    OF SUCCESSFUL PROJECTS
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-orange-400 mb-1">4000+</div>
                  <div className={cn(
                    'text-sm',
                    isBlue ? 'text-blue-200' : 'text-gray-500'
                  )}>
                    MANAGERS WHO HAVE RECEIVED OUR TRAINING
                  </div>
                </div>
                <div>
                  <div className="text-3xl font-bold text-orange-400 mb-1">30+</div>
                  <div className={cn(
                    'text-sm',
                    isBlue ? 'text-blue-200' : 'text-gray-500'
                  )}>
                    DEVELOPED INTEGRATIONS
                  </div>
                </div>
              </div>

              {/* Why choose Setmee */}
              <div className={cn(
                'rounded-lg p-6',
                isBlue ? 'bg-blue-700' : 'bg-gray-100'
              )}
              >
                <h3 className={cn(
                  'text-xl font-semibold mb-4',
                  isBlue ? 'text-white' : 'text-gray-900'
                )}>
                  Why choose Setmee?
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className={isBlue ? 'text-white' : 'text-gray-700'}>
                      Certified Kommo partnership
                    </span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className={isBlue ? 'text-white' : 'text-gray-700'}>
                      10 years of proven experience
                    </span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className={isBlue ? 'text-white' : 'text-gray-700'}>
                      Complex problem-solving expertise
                    </span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-green-400 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className={isBlue ? 'text-white' : 'text-gray-700'}>
                      Custom integration development
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </FadeInSection>
        </div>
      </div>
    </section>
  );
};

export default CertifiedPartnerSection;
