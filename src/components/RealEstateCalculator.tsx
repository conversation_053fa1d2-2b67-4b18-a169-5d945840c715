'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import Button from './Button';

interface CalculatorData {
  leads: number;
  conversion: number;
  commission: number;
}

interface CalculatorResults {
  currentRevenue: number;
  improvedRevenue: number;
  additionalRevenue: number;
  currentDeals: number;
  improvedDeals: number;
  additionalDeals: number;
}

const RealEstateCalculator: React.FC = () => {
  const [data, setData] = useState<CalculatorData>({
    leads: 50,
    conversion: 15,
    commission: 5000
  });

  const [results, setResults] = useState<CalculatorResults>({
    currentRevenue: 0,
    improvedRevenue: 0,
    additionalRevenue: 0,
    currentDeals: 0,
    improvedDeals: 0,
    additionalDeals: 0
  });

  const [errors, setErrors] = useState<Partial<CalculatorData>>({});

  // Calculate results whenever data changes
  useEffect(() => {
    const currentDeals = (data.leads * data.conversion) / 100;
    const currentRevenue = currentDeals * data.commission;
    
    // Improved conversion rate (typically 25-30% with CRM)
    const improvedConversion = Math.min(data.conversion + 10, 30);
    const improvedDeals = (data.leads * improvedConversion) / 100;
    const improvedRevenue = improvedDeals * data.commission;
    
    const additionalDeals = improvedDeals - currentDeals;
    const additionalRevenue = improvedRevenue - currentRevenue;

    setResults({
      currentRevenue,
      improvedRevenue,
      additionalRevenue,
      currentDeals,
      improvedDeals,
      additionalDeals
    });
  }, [data]);

  const validateInput = (field: keyof CalculatorData, value: number) => {
    const newErrors = { ...errors };
    
    if (field === 'leads' && (value < 1 || value > 10000)) {
      newErrors.leads = value;
    } else if (field === 'conversion' && (value < 0 || value > 100)) {
      newErrors.conversion = value;
    } else if (field === 'commission' && (value < 100 || value > 1000000)) {
      newErrors.commission = value;
    } else {
      delete newErrors[field];
    }
    
    setErrors(newErrors);
  };

  const handleInputChange = (field: keyof CalculatorData, value: string) => {
    const numValue = parseFloat(value) || 0;
    validateInput(field, numValue);
    setData(prev => ({ ...prev, [field]: numValue }));
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return Math.round(num * 10) / 10;
  };

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="bg-gradient-to-r from-orange-500 to-red-600 p-6 text-white">
        <h3 className="text-2xl font-bold mb-2">Real Estate Revenue Calculator</h3>
        <p className="text-orange-100">Calculate your potential revenue increase with Kommo CRM</p>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Input Section */}
          <div className="space-y-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Your Current Metrics</h4>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Average leads per month
              </label>
              <input
                type="number"
                value={data.leads || ''}
                onChange={(e) => handleInputChange('leads', e.target.value)}
                placeholder="50"
                min="1"
                max="10000"
                className={cn(
                  "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors",
                  errors.leads ? "border-red-500" : "border-gray-300"
                )}
              />
              {errors.leads && (
                <p className="text-red-500 text-sm mt-1">Please enter a value between 1 and 10,000</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current conversion rate (%)
              </label>
              <input
                type="number"
                value={data.conversion || ''}
                onChange={(e) => handleInputChange('conversion', e.target.value)}
                placeholder="15"
                min="0"
                max="100"
                step="0.1"
                className={cn(
                  "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors",
                  errors.conversion ? "border-red-500" : "border-gray-300"
                )}
              />
              {errors.conversion && (
                <p className="text-red-500 text-sm mt-1">Please enter a value between 0 and 100</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Average commission per deal ($)
              </label>
              <input
                type="number"
                value={data.commission || ''}
                onChange={(e) => handleInputChange('commission', e.target.value)}
                placeholder="5000"
                min="100"
                max="1000000"
                className={cn(
                  "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors",
                  errors.commission ? "border-red-500" : "border-gray-300"
                )}
              />
              {errors.commission && (
                <p className="text-red-500 text-sm mt-1">Please enter a value between $100 and $1,000,000</p>
              )}
            </div>
          </div>

          {/* Results Section */}
          <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-lg p-6 border border-orange-200">
            <h4 className="text-lg font-semibold text-orange-900 mb-6">Potential Results with Kommo CRM</h4>
            
            <div className="space-y-4">
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-700">Current deals/month:</span>
                <span className="font-semibold text-gray-900">{formatNumber(results.currentDeals)}</span>
              </div>
              
              <div className="flex justify-between items-center py-2">
                <span className="text-gray-700">Current monthly revenue:</span>
                <span className="font-semibold text-gray-900">{formatCurrency(results.currentRevenue)}</span>
              </div>
              
              <div className="border-t border-orange-200 pt-4">
                <div className="flex justify-between items-center py-2">
                  <span className="text-orange-700">Improved deals/month:</span>
                  <span className="font-semibold text-orange-900">{formatNumber(results.improvedDeals)}</span>
                </div>
                
                <div className="flex justify-between items-center py-2">
                  <span className="text-orange-700">Improved monthly revenue:</span>
                  <span className="font-semibold text-orange-900">{formatCurrency(results.improvedRevenue)}</span>
                </div>
              </div>
              
              <div className="bg-green-100 border border-green-300 rounded-lg p-4 mt-4">
                <div className="flex justify-between items-center">
                  <span className="text-green-700 font-medium">Additional revenue:</span>
                  <span className="font-bold text-green-800 text-xl">{formatCurrency(results.additionalRevenue)}/month</span>
                </div>
                <div className="flex justify-between items-center mt-2">
                  <span className="text-green-700 font-medium">Annual increase:</span>
                  <span className="font-bold text-green-800 text-lg">{formatCurrency(results.additionalRevenue * 12)}</span>
                </div>
              </div>
            </div>
            
            <Button 
              variant="primary" 
              className="w-full mt-6 bg-orange-600 hover:bg-orange-700"
              size="lg"
            >
              Get Demo Access to CRM
            </Button>
            
            <p className="text-sm text-gray-600 text-center mt-3">
              See how these results are achievable with proper CRM implementation
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RealEstateCalculator;
