'use client';

import React, { useState } from 'react';
import Card from '@/components/Card';
import Button from '@/components/Button';
import FadeInSection from '@/components/FadeInSection';
import AuditModal from '@/components/AuditModal';

interface PricingPlan {
  id: string;
  name: string;
  price: string;
  description: string;
  targetAudience: string;
  features: string[];
  isPopular?: boolean;
}

const pricingPlans: PricingPlan[] = [
  {
    id: 'basic',
    name: 'Basic Plan',
    price: '$1,200',
    description: 'Perfect for small businesses and startups beginning their automation journey',
    targetAudience: 'Small business, startups, beginning automation',
    features: [
      'Current process audit',
      'Standard sales funnel setup',
      'Integration of up to 2 channels (website, email, messenger)',
      'Custom fields and basic automation (lead distribution, notifications)',
      'Import up to 2,000 contacts/leads',
      'Training for up to 5 users (online session + recording + materials)',
      '30 days post-launch support (email, chat)'
    ]
  },
  {
    id: 'standard',
    name: 'Standard Plan',
    price: '$3,000',
    description: 'Ideal for growing teams with multiple sales channels and advanced automation needs',
    targetAudience: 'Growing teams with multiple sales channels and advanced automation',
    features: [
      'Deep business process audit and CRM concept development',
      'Custom funnel setup with advanced stages',
      'Service integrations (website, telephony, messengers, email campaigns, etc.)',
      'Advanced automation: triggers, tasks, auto-mailings, document generation',
      'Data migration from legacy systems (up to 10,000 records)',
      'Training for up to 15 users, access to video tutorials',
      '30 days support (priority email, chat, 2 online Q&A sessions)'
    ],
    isPopular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise Plan',
    price: '$6,000+',
    description: 'Comprehensive solution for medium and large businesses with complex processes',
    targetAudience: 'Medium and large business, complex processes, individual requirements',
    features: [
      'Complete process audit (workshops with key employees)',
      'CRM architecture: multi-level funnels, access rights, custom fields',
      'Unlimited integrations (ERP, accounting, marketing, warehouse, etc.)',
      'Custom widgets, scripts, and automation development',
      'Data migration of any volume (cleaning, mapping)',
      'Training for unlimited users (live sessions, recordings, individual instructions)',
      '60 days premium support (dedicated manager, SLA, optimization)',
      'Quarterly audit and CRM optimization'
    ]
  }
];

const commonFeatures = [
  'Certified implementation team',
  'Guaranteed security and access control',
  'Transparent timelines and work stages',
  'Possibility of subsequent scaling and support'
];

interface PricingPlansProps {
  className?: string;
}

const PricingPlans: React.FC<PricingPlansProps> = ({ className = '' }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>('');

  const handleAuditRequest = (planName: string) => {
    setSelectedPlan(planName);
    setIsModalOpen(true);
  };

  return (
    <section className={`py-20 bg-gray-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Kommo CRM Implementation Plans
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Transparent pricing and clear deliverables. Choose the plan that fits your business needs, 
              or let us recommend the best option after a free audit.
            </p>
          </div>
        </FadeInSection>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {pricingPlans.map((plan, index) => (
            <FadeInSection key={plan.id} delay={200 + index * 200}>
              <Card 
                variant="outlined" 
                className={`relative p-8 h-full hover:shadow-xl transition-all duration-300 ${
                  plan.isPopular ? 'border-blue-500 border-2 bg-blue-50' : 'bg-white'
                }`}
              >
                {plan.isPopular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold">
                      Most Popular
                    </span>
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="text-4xl font-bold text-blue-600 mb-2">{plan.price}</div>
                  <p className="text-gray-600 text-sm mb-4">{plan.description}</p>
                  <div className="bg-gray-100 rounded-lg p-3">
                    <p className="text-sm text-gray-700">
                      <span className="font-semibold">For:</span> {plan.targetAudience}
                    </p>
                  </div>
                </div>

                <div className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start">
                      <span className="text-green-500 text-lg mr-3 mt-0.5 flex-shrink-0">✓</span>
                      <span className="text-gray-700 text-sm leading-relaxed">{feature}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-auto">
                  <Button
                    variant="primary"
                    size="lg"
                    className={`w-full ${
                      plan.isPopular
                        ? 'bg-blue-600 hover:bg-blue-700'
                        : 'bg-gray-800 hover:bg-gray-900'
                    }`}
                    onClick={() => handleAuditRequest(plan.name)}
                  >
                    Request a Free Audit
                  </Button>
                </div>
              </Card>
            </FadeInSection>
          ))}
        </div>

        {/* Common Features */}
        <FadeInSection delay={800}>
          <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-gray-900 mb-6 text-center">
              All Plans Include:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {commonFeatures.map((feature, index) => (
                <div key={index} className="flex items-center">
                  <span className="text-blue-500 text-lg mr-3">✓</span>
                  <span className="text-gray-700">{feature}</span>
                </div>
              ))}
            </div>
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
              <p className="text-sm text-gray-700 italic">
                <span className="font-semibold">Note:</span> Final pricing is determined after a free audit 
                and requirements clarification. All plans can be customized to your specific needs.
              </p>
            </div>
          </div>
        </FadeInSection>

        {/* Audit Modal */}
        <AuditModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          planName={selectedPlan}
        />
      </div>
    </section>
  );
};

export default PricingPlans;
