'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRoiStore } from '@/store/roiStore';
import Button from './Button';
import Tooltip from './Tooltip';
import { cn } from '@/lib/utils';
import { INDUSTRY_OPTIONS, CHANNEL_OPTIONS, FIELD_TOOLTIPS } from '@/data/roiCalculatorData';

const RoiForm: React.FC = () => {
  const {
    formData,
    setFormData,
    calculateResults,
    showResults
  } = useRoiStore();
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showAdvancedFields, setShowAdvancedFields] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    let processedValue: string | number | string[] = value;

    if (type === 'number') {
      processedValue = parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      const checkbox = e.target as HTMLInputElement;
      const currentChannels = formData.channels || [];
      if (checkbox.checked) {
        processedValue = [...currentChannels, value];
      } else {
        processedValue = currentChannels.filter(channel => channel !== value);
      }
      setFormData({ channels: processedValue });
      return;
    }

    setFormData({ [name]: processedValue });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (formData.leads <= 0) {
      newErrors.leads = 'Please enter a valid number of leads';
    }

    if (formData.conversion <= 0 || formData.conversion >= 100) {
      newErrors.conversion = 'Conversion rate must be between 1-99%';
    }

    if (formData.avgDealSize <= 0) {
      newErrors.avgDealSize = 'Please enter a valid deal size';
    }

    // Validate optional advanced fields if they are filled
    if (formData.timePerLead && (formData.timePerLead < 1 || formData.timePerLead > 180)) {
      newErrors.timePerLead = 'Time per lead must be between 1-180 minutes';
    }

    if (formData.managers && formData.managers <= 0) {
      newErrors.managers = 'Number of managers must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (validateForm()) {
      calculateResults();
    }
  };

  return (
    <div
      id="roi-form"
      className={cn(
        "bg-white rounded-xl shadow-lg p-8 transition-all duration-500",
        showResults && "transform -translate-y-4"
      )}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Leads per month */}
        <div>
          <Tooltip content={FIELD_TOOLTIPS.leads}>
            <label htmlFor="leads" className="block text-sm font-semibold text-gray-700 mb-2 cursor-help">
              Leads per month *
              <span className="ml-1 text-gray-400">ⓘ</span>
            </label>
          </Tooltip>
          <input
            type="number"
            id="leads"
            name="leads"
            value={formData.leads || ''}
            onChange={handleInputChange}
            placeholder="e.g., 150"
            className={cn(
              "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
              errors.leads ? "border-red-500" : "border-gray-300"
            )}
            min="1"
          />
          {errors.leads && (
            <p className="mt-1 text-sm text-red-600">{errors.leads}</p>
          )}
        </div>

        {/* Current conversion rate */}
        <div>
          <Tooltip content={FIELD_TOOLTIPS.conversion}>
            <label htmlFor="conversion" className="block text-sm font-semibold text-gray-700 mb-2 cursor-help">
              Current conversion rate (%) *
              <span className="ml-1 text-gray-400">ⓘ</span>
            </label>
          </Tooltip>
          <input
            type="number"
            id="conversion"
            name="conversion"
            value={formData.conversion || ''}
            onChange={handleInputChange}
            placeholder="What % of leads currently become sales?"
            className={cn(
              "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
              errors.conversion ? "border-red-500" : "border-gray-300"
            )}
            min="1"
            max="99"
            step="0.1"
          />
          {errors.conversion && (
            <p className="mt-1 text-sm text-red-600">{errors.conversion}</p>
          )}
        </div>

        {/* Average deal size */}
        <div>
          <Tooltip content={FIELD_TOOLTIPS.avgDealSize}>
            <label htmlFor="avgDealSize" className="block text-sm font-semibold text-gray-700 mb-2 cursor-help">
              Average deal size ($) *
              <span className="ml-1 text-gray-400">ⓘ</span>
            </label>
          </Tooltip>
          <input
            type="number"
            id="avgDealSize"
            name="avgDealSize"
            value={formData.avgDealSize || ''}
            onChange={handleInputChange}
            placeholder="Average value of a single sale"
            className={cn(
              "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
              errors.avgDealSize ? "border-red-500" : "border-gray-300"
            )}
            min="1"
          />
          {errors.avgDealSize && (
            <p className="mt-1 text-sm text-red-600">{errors.avgDealSize}</p>
          )}
        </div>

        {/* Refine Calculation Toggle */}
        <div className="text-center py-4">
          <button
            type="button"
            onClick={() => setShowAdvancedFields(!showAdvancedFields)}
            className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors"
          >
            {showAdvancedFields ? '▲ Hide Advanced Fields' : '▼ Refine Calculation'}
          </button>
          {!showAdvancedFields && (
            <p className="text-xs text-gray-500 mt-1">
              The more precise your input, the more personalized the analysis you&apos;ll receive.
            </p>
          )}
        </div>

        {/* Advanced Fields */}
        <AnimatePresence>
          {showAdvancedFields && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="space-y-6 border-t pt-6"
            >
            {/* Time spent per lead */}
            <div>
              <Tooltip content={FIELD_TOOLTIPS.timePerLead}>
                <label htmlFor="timePerLead" className="block text-sm font-semibold text-gray-700 mb-2 cursor-help">
                  Time spent per lead (minutes)
                  <span className="ml-1 text-gray-400">ⓘ</span>
                </label>
              </Tooltip>
              <input
                type="number"
                id="timePerLead"
                name="timePerLead"
                value={formData.timePerLead || ''}
                onChange={handleInputChange}
                placeholder="How many minutes are spent on routine tasks?"
                className={cn(
                  "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
                  errors.timePerLead ? "border-red-500" : "border-gray-300"
                )}
                min="1"
                max="180"
              />
              {errors.timePerLead && (
                <p className="mt-1 text-sm text-red-600">{errors.timePerLead}</p>
              )}
              <p className="mt-1 text-sm text-gray-500">Optional: helps calculate time savings</p>
            </div>

            {/* Industry/Niche */}
            <div>
              <Tooltip content={FIELD_TOOLTIPS.niche}>
                <label htmlFor="niche" className="block text-sm font-semibold text-gray-700 mb-2 cursor-help">
                  Industry/Niche
                  <span className="ml-1 text-gray-400">ⓘ</span>
                </label>
              </Tooltip>
              <select
                id="niche"
                name="niche"
                value={formData.niche || ''}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors"
              >
                {INDUSTRY_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Managers in Sales Department */}
            <div>
              <Tooltip content={FIELD_TOOLTIPS.managers}>
                <label htmlFor="managers" className="block text-sm font-semibold text-gray-700 mb-2 cursor-help">
                  Managers in Sales Department
                  <span className="ml-1 text-gray-400">ⓘ</span>
                </label>
              </Tooltip>
              <input
                type="number"
                id="managers"
                name="managers"
                value={formData.managers || ''}
                onChange={handleInputChange}
                placeholder="Number of managers in the sales department"
                className={cn(
                  "w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors",
                  errors.managers ? "border-red-500" : "border-gray-300"
                )}
                min="1"
              />
              {errors.managers && (
                <p className="mt-1 text-sm text-red-600">{errors.managers}</p>
              )}
            </div>

            {/* Channels Used */}
            <div>
              <Tooltip content={FIELD_TOOLTIPS.channels}>
                <label className="block text-sm font-semibold text-gray-700 mb-3 cursor-help">
                  Channels Used
                  <span className="ml-1 text-gray-400">ⓘ</span>
                </label>
              </Tooltip>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {CHANNEL_OPTIONS.map(channel => (
                  <label key={channel.value} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      name="channels"
                      value={channel.value}
                      checked={formData.channels?.includes(channel.value) || false}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="text-sm text-gray-700">{channel.label}</span>
                  </label>
                ))}
              </div>
            </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Submit button */}
        <Button
          type="submit"
          variant="primary"
          size="lg"
          className="w-full text-base md:text-lg py-4"
        >
          Calculate ROI
        </Button>
      </form>
    </div>
  );
};

export default RoiForm;
