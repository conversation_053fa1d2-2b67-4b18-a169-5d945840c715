import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Image from 'next/image';

const partners = [
  { name: 'Kommo Certified Partner', logo: '/Crtifikat.webp', verified: true },
  { name: 'MLS Integration', logo: '🏘️', verified: true },
  { name: 'Real Estate CRM Specialist', logo: '🏆', verified: true },
  { name: 'Make.com Partner', logo: '🔗', verified: true }
];

const certifications = [
  {
    title: 'Kommo Certified Implementation Partner',
    description: 'Official certification for real estate CRM implementations',
    icon: '🏅',
    year: '2023'
  },
  {
    title: 'Real Estate Technology Specialist',
    description: 'Specialized expertise in property management systems',
    icon: '🏠',
    year: '2022'
  },
  {
    title: 'CRM Integration Expert',
    description: 'Advanced certification in CRM integrations and automation',
    icon: '⚙️',
    year: '2023'
  }
];

const achievements = [
  {
    metric: '320+',
    label: 'Real Estate Projects',
    description: 'Successfully implemented CRM solutions',
    icon: '📊'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In real estate technology solutions',
    icon: '⏰'
  },
  {
    metric: '4000+',
    label: 'Agents Trained',
    description: 'On Kommo CRM best practices',
    icon: '👥'
  },
  {
    metric: '98%',
    label: 'Client Satisfaction',
    description: 'Based on post-implementation surveys',
    icon: '⭐'
  }
];

const clientLogos = [
  { name: 'Premium Properties', logo: '🏢' },
  { name: 'Metro Realty', logo: '🏙️' },
  { name: 'Coastal Real Estate', logo: '🌊' },
  { name: 'Urban Living', logo: '🏘️' },
  { name: 'Elite Properties', logo: '💎' },
  { name: 'Residential Plus', logo: '🏡' }
];

const RealEstateTrust: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Certifications Section */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Certified Real Estate CRM Experts
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Trust your real estate CRM implementation to certified professionals with proven expertise
          </p>
        </div>
      </FadeInSection>

      {/* Main Certification Badge */}
      <FadeInSection delay={200}>
        <div className="flex justify-center mb-12">
          <div className="relative">
            <div className="w-48 h-48 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-full flex items-center justify-center shadow-2xl">
              <div className="text-center text-white">
                <div className="text-4xl mb-2">🏆</div>
                <div className="font-bold text-lg">Certified</div>
                <div className="text-sm">Kommo Partner</div>
              </div>
            </div>
            <div className="absolute -top-2 -right-2 w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white text-xl">
              ✓
            </div>
          </div>
        </div>
      </FadeInSection>

      {/* Certifications Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {certifications.map((cert, index) => (
          <FadeInSection key={index} delay={400 + index * 150}>
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6 text-center hover:shadow-xl transition-shadow duration-300">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 rounded-full flex items-center justify-center text-3xl text-orange-600 mx-auto mb-4">
                {cert.icon}
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">{cert.title}</h3>
              <p className="text-gray-600 text-sm mb-3">{cert.description}</p>
              <div className="inline-block bg-orange-100 text-orange-800 text-xs font-semibold px-3 py-1 rounded-full">
                Certified {cert.year}
              </div>
            </div>
          </FadeInSection>
        ))}
      </div>

      {/* Achievements Section */}
      <FadeInSection delay={800}>
        <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 md:p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Proven Track Record in Real Estate
            </h3>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Our experience speaks for itself - we've helped hundreds of real estate professionals 
              transform their business with CRM technology
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <FadeInSection key={index} delay={1000 + index * 150}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                    {achievement.icon}
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-orange-400 mb-2">
                    {achievement.metric}
                  </div>
                  <div className="text-lg font-semibold mb-2">
                    {achievement.label}
                  </div>
                  <div className="text-gray-300 text-sm">
                    {achievement.description}
                  </div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </FadeInSection>

      {/* Partners & Integrations */}
      <FadeInSection delay={1400}>
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Trusted Partners & Integrations
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We work with industry-leading platforms to provide comprehensive real estate solutions
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {partners.map((partner, index) => (
            <FadeInSection key={index} delay={1600 + index * 100}>
              <div className="bg-white rounded-lg border border-gray-200 p-6 text-center hover:shadow-lg transition-shadow duration-300">
                {partner.logo.startsWith('/') ? (
                  <div className="w-16 h-16 mx-auto mb-3 relative">
                    <Image
                      src={partner.logo}
                      alt={partner.name}
                      fill
                      className="object-contain"
                    />
                  </div>
                ) : (
                  <div className="text-4xl mb-3">{partner.logo}</div>
                )}
                <div className="text-sm font-medium text-gray-900">{partner.name}</div>
                {partner.verified && (
                  <div className="inline-flex items-center mt-2 text-xs text-green-600">
                    <span className="mr-1">✓</span>
                    Verified Partner
                  </div>
                )}
              </div>
            </FadeInSection>
          ))}
        </div>
      </FadeInSection>

      {/* Client Logos */}
      <FadeInSection delay={2000}>
        <div className="bg-gray-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Trusted by Leading Real Estate Companies
            </h3>
            <p className="text-gray-600">
              Join hundreds of successful real estate professionals using our CRM solutions
            </p>
          </div>

          <div className="grid grid-cols-3 md:grid-cols-6 gap-6">
            {clientLogos.map((client, index) => (
              <FadeInSection key={index} delay={2200 + index * 100}>
                <div className="text-center opacity-70 hover:opacity-100 transition-opacity duration-300">
                  <div className="text-3xl mb-2">{client.logo}</div>
                  <div className="text-xs text-gray-600">{client.name}</div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </FadeInSection>

      {/* Security & Compliance */}
      <FadeInSection delay={2400}>
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-200">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center text-2xl text-green-600 mx-auto mb-4">
              🔒
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Security & Compliance Guaranteed
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto mb-6">
              Your client data is protected with enterprise-grade security. We ensure full compliance 
              with real estate industry regulations and data protection standards.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">GDPR Compliant</span>
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">SSL Encrypted</span>
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">SOC 2 Certified</span>
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full">Real Estate Compliant</span>
            </div>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default RealEstateTrust;
