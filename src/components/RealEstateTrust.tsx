import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';
import Image from 'next/image';

const partners = [
  { name: 'Kommo Certified Partner', logo: '/Crtifikat.webp', verified: true },
  { name: 'MLS Integration', logo: '🏘️', verified: true },
  { name: 'Real Estate CRM Specialist', logo: '🏆', verified: true },
  { name: 'Make.com Partner', logo: '🔗', verified: true }
];

const achievements = [
  {
    metric: '320+',
    label: 'Real Estate Projects',
    description: 'Successfully implemented CRM solutions',
    icon: '📊'
  },
  {
    metric: '10+',
    label: 'Years Experience',
    description: 'In real estate technology solutions',
    icon: '⏰'
  },
  {
    metric: '4000+',
    label: 'Agents Trained',
    description: 'On Kommo CRM best practices',
    icon: '👥'
  },
  {
    metric: '98%',
    label: 'Client Satisfaction',
    description: 'Based on post-implementation surveys',
    icon: '⭐'
  }
];

const clientLogos = [
  { name: 'Premium Properties', logo: '🏢' },
  { name: 'Metro Realty', logo: '🏙️' },
  { name: 'Coastal Real Estate', logo: '🌊' },
  { name: 'Urban Living', logo: '🏘️' },
  { name: 'Elite Properties', logo: '💎' },
  { name: 'Residential Plus', logo: '🏡' }
];

const RealEstateTrust: React.FC = () => {
  return (
    <div className="space-y-16">
      {/* Certifications Section */}
      <FadeInSection>
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Certified Real Estate CRM Experts
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Trust your real estate CRM implementation to certified professionals with proven expertise
          </p>
        </div>
      </FadeInSection>

      {/* Main Certification Badge */}
      <FadeInSection delay={200}>
        <div className="text-center mb-12">
          <div className="inline-block bg-white rounded-lg p-6 shadow-xl max-w-3xl mx-auto">
            <img
              src="/Crtifikat.webp"
              alt="Kommo Partnership Certificate"
              className="w-full max-w-2xl mx-auto rounded-lg border border-gray-200"
            />
          </div>
        </div>
      </FadeInSection>

{/* Achievements Section */}
      <FadeInSection delay={800}>
        <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 md:p-12 text-white">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Proven Track Record in Real Estate
            </h3>
            <p className="text-gray-300 max-w-2xl mx-auto">
              Our experience speaks for itself - we've helped hundreds of real estate professionals 
              transform their business with CRM technology
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <FadeInSection key={index} delay={1000 + index * 150}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-600 rounded-full flex items-center justify-center text-2xl mx-auto mb-4">
                    {achievement.icon}
                  </div>
                  <div className="text-3xl md:text-4xl font-bold text-orange-400 mb-2">
                    {achievement.metric}
                  </div>
                  <div className="text-lg font-semibold mb-2">
                    {achievement.label}
                  </div>
                  <div className="text-gray-300 text-sm">
                    {achievement.description}
                  </div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </FadeInSection>


      {/* Client Logos */}
      <FadeInSection delay={1400}>
        <div className="bg-gray-50 rounded-2xl p-8">
          <div className="text-center mb-8">
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Trusted by Leading Real Estate Companies
            </h3>
            <p className="text-gray-600">
              Join hundreds of successful real estate professionals using our CRM solutions
            </p>
          </div>

          <div className="grid grid-cols-3 md:grid-cols-6 gap-6">
            {clientLogos.map((client, index) => (
              <FadeInSection key={index} delay={2200 + index * 100}>
                <div className="text-center opacity-70 hover:opacity-100 transition-opacity duration-300">
                  <div className="text-3xl mb-2">{client.logo}</div>
                  <div className="text-xs text-gray-600">{client.name}</div>
                </div>
              </FadeInSection>
            ))}
          </div>
        </div>
      </FadeInSection>

    </div>
  );
};

export default RealEstateTrust;
