import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface LinkButtonProps {
  href: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  className?: string;
  external?: boolean;
}

const LinkButton: React.FC<LinkButtonProps> = ({
  href,
  variant = 'primary',
  size = 'md',
  children,
  className,
  external = false
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';

  const variants = {
    primary: 'text-white shadow-lg hover:shadow-xl transform hover:scale-105 focus:ring-blue-500',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-lg hover:shadow-xl transform hover:scale-105',
    outline: 'border-2 text-white transform hover:scale-105 focus:ring-blue-500',
    ghost: 'hover:bg-blue-50 focus:ring-blue-500'
  };

  const sizes = {
    sm: 'px-4 py-2 text-sm',
    md: 'px-6 py-3 text-base',
    lg: 'px-10 py-4 text-lg'
  };

  // Custom styles for blue variants
  const getCustomStyle = () => {
    const baseColor = 'rgb(37, 99, 235)';

    switch (variant) {
      case 'primary':
        return { backgroundColor: baseColor };
      case 'outline':
        return {
          borderColor: baseColor,
          color: baseColor,
          backgroundColor: 'transparent'
        };
      case 'ghost':
        return { color: baseColor };
      default:
        return {};
    }
  };

  const buttonClasses = cn(
    baseClasses,
    variants[variant],
    sizes[size],
    className
  );

  if (external) {
    return (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className={buttonClasses}
        style={getCustomStyle()}
      >
        {children}
      </a>
    );
  }

  return (
    <Link href={href} className={buttonClasses} style={getCustomStyle()}>
      {children}
    </Link>
  );
};

export default LinkButton;
