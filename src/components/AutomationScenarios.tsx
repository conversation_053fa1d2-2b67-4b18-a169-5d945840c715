import React from 'react';
import Card from '@/components/Card';
import FadeInSection from '@/components/FadeInSection';

interface AutomationScenario {
  id: string;
  title: string;
  description: string;
  icon: string;
}

const automationScenarios: AutomationScenario[] = [
  {
    id: 'lead-processing',
    title: 'Lead Processing Automation',
    description: 'Automatic lead distribution, qualification, and follow-up scheduling based on source and criteria.',
    icon: '🎯'
  },
  {
    id: 'messenger-integration',
    title: 'Messenger Integration',
    description: 'Unified communication hub connecting WhatsApp, Telegram, Facebook Messenger, and live chat.',
    icon: '💬'
  },
  {
    id: 'task-management',
    title: 'Manager Task Control',
    description: 'Automated task assignment, deadline tracking, and performance monitoring for sales teams.',
    icon: '📋'
  },
  {
    id: 'document-automation',
    title: 'Document Automation',
    description: 'Auto-generation and sending of contracts, invoices, proposals, and follow-up emails.',
    icon: '📄'
  }
];

interface AutomationScenariosProps {
  className?: string;
}

const AutomationScenarios: React.FC<AutomationScenariosProps> = ({ className = '' }) => {
  return (
    <section className={`py-16 bg-gradient-to-br from-blue-50 to-indigo-50 border-4 border-red-500 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Common Automation Scenarios
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              See how businesses like yours solve everyday challenges with Kommo automation
            </p>
          </div>
        </FadeInSection>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {automationScenarios.map((scenario, index) => (
            <FadeInSection key={scenario.id} delay={200 + index * 100}>
              <Card 
                variant="outlined" 
                className="p-6 text-center h-full hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-white border-gray-200"
              >
                <div className="text-4xl mb-4">{scenario.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 leading-tight">
                  {scenario.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {scenario.description}
                </p>
              </Card>
            </FadeInSection>
          ))}
        </div>

        <FadeInSection delay={600}>
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500 italic">
              These are just a few examples. We customize automation to fit your specific business processes.
            </p>
          </div>
        </FadeInSection>
      </div>
    </section>
  );
};

export default AutomationScenarios;
