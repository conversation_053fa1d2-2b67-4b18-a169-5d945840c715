'use client';

import React from 'react';
import RoiForm from './RoiFormSimple';
import RoiResults from './RoiResults';
import RoiCTA from './RoiCTA';
import LeadCaptureModal from './LeadCaptureModal';

const Calculator: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto">
      {/* Form */}
      <RoiForm />
      
      {/* Results */}
      <RoiResults />
      
      {/* CTA */}
      <RoiCTA />
      
      {/* Modal */}
      <LeadCaptureModal />
    </div>
  );
};

export default Calculator;
