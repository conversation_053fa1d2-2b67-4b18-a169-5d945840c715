'use client';

import React, { useEffect, useState } from 'react';
import AnimatedCT<PERSON> from './AnimatedCTA';

interface PersonalizedCTAProps {
  firstVisitText: string;
  returningVisitText: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  onClick?: () => void;
  href?: string;
}

const PersonalizedCTA: React.FC<PersonalizedCTAProps> = ({
  firstVisitText,
  returningVisitText,
  variant = 'primary',
  size = 'md',
  className,
  onClick,
  href
}) => {
  const [isReturningVisitor, setIsReturningVisitor] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    // Check if user has visited before
    const hasVisited = localStorage.getItem('setmee-visited');
    
    if (hasVisited) {
      setIsReturningVisitor(true);
    } else {
      // Mark as visited for future visits
      localStorage.setItem('setmee-visited', 'true');
    }
    
    setIsLoaded(true);
  }, []);

  if (!isLoaded) {
    // Return a placeholder to prevent hydration mismatch
    return (
      <AnimatedCTA
        variant={variant}
        size={size}
        className={className}
        onClick={onClick}
        href={href}
      >
        {firstVisitText}
      </AnimatedCTA>
    );
  }

  return (
    <AnimatedCTA
      variant={variant}
      size={size}
      className={className}
      onClick={onClick}
      href={href}
    >
      {isReturningVisitor ? returningVisitText : firstVisitText}
    </AnimatedCTA>
  );
};

export default PersonalizedCTA;
