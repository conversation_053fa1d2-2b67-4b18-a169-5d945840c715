'use client';

import React, { useEffect, useState } from 'react';
import Button from './Button';
import LinkButton from './LinkButton';
import { cn } from '@/lib/utils';

interface AnimatedCTAProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  shakeDelay?: number; // in milliseconds
  onClick?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  href?: string;
}

const AnimatedCTA: React.FC<AnimatedCTAProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  className,
  shakeDelay = 15000, // 15 seconds default
  onClick,
  disabled,
  isLoading,
  href
}) => {
  const [shouldShake, setShouldShake] = useState(false);
  const [hasInteracted, setHasInteracted] = useState(false);

  useEffect(() => {
    if (hasInteracted) return;

    const timer = setTimeout(() => {
      setShouldShake(true);
      // Stop shaking after animation completes
      setTimeout(() => setShouldShake(false), 500);
    }, shakeDelay);

    return () => clearTimeout(timer);
  }, [shakeDelay, hasInteracted]);

  const handleClick = () => {
    setHasInteracted(true);
    setShouldShake(false);
    onClick?.();
  };

  const handleMouseEnter = () => {
    setHasInteracted(true);
    setShouldShake(false);
  };

  if (href) {
    return (
      <LinkButton
        href={href}
        variant={variant}
        size={size}
        className={cn(
          'transition-all duration-200',
          shouldShake && 'animate-shake',
          className
        )}
      >
        {children}
      </LinkButton>
    );
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={cn(
        'transition-all duration-200',
        shouldShake && 'animate-shake',
        className
      )}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      disabled={disabled}
      isLoading={isLoading}
    >
      {children}
    </Button>
  );
};

export default AnimatedCTA;
