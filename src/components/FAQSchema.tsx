import React from 'react';

interface FAQSchemaProps {
  className?: string;
}

const FAQSchema: React.FC<FAQSchemaProps> = ({ className = '' }) => {
  const faqSchemaData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": [
      {
        "@type": "Question",
        "name": "How long does it take to implement Kommo CRM?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "On average, 14 to 30 business days, depending on process complexity and the number of channels (website, telephony, messengers). Simple projects can be launched in a week."
        }
      },
      {
        "@type": "Question",
        "name": "When will I see the first results?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Within the first week after launch: automations kick in, leads stop getting lost, and managers start tracking deals. After that—conversion growth and better control."
        }
      },
      {
        "@type": "Question",
        "name": "What do you need from us?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Just the involvement of a responsible manager or leader at the start—for interviews and structure approval. We handle everything else."
        }
      },
      {
        "@type": "Question",
        "name": "What if we already have Kommo?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "We often work with \"tired\" CRMs: we audit, find errors, optimize pipelines, and build new logic. Sometimes it's easier to redo than to \"fix.\""
        }
      },
      {
        "@type": "Question",
        "name": "Do you deliver a turnkey solution?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes. From audit to setup, integrations, training, and support. You'll have a ready-to-use CRM you can start working with immediately."
        }
      },
      {
        "@type": "Question",
        "name": "Will there be automations?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes. We set up pipelines with triggers, bots, and notifications. For example: website lead → auto-reply → task → notification → deal → stage → report. All without manual routine."
        }
      },
      {
        "@type": "Question",
        "name": "Do you train the team?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Absolutely. We'll run a live Zoom session or record instructions for distributed teams. Plus, we provide a video guide for each stage."
        }
      },
      {
        "@type": "Question",
        "name": "What if something breaks?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "We offer post-launch support. If needed, we can provide ongoing CRM maintenance (optional)."
        }
      },
      {
        "@type": "Question",
        "name": "How much does implementation cost?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "The price depends on the scope, number of channels, and logic complexity. We tailor the solution to your needs and budget—after a free audit."
        }
      },
      {
        "@type": "Question",
        "name": "Can you connect the website, email, and messengers?",
        "acceptedAnswer": {
          "@type": "Answer",
          "text": "Yes, all included in the implementation package. Kommo connects all communications in one system. No more searching for conversations across platforms."
        }
      }
    ]
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(faqSchemaData) }}
      className={className}
    />
  );
};

export default FAQSchema;
