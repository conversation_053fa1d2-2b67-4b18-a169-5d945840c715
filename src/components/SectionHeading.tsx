import React from 'react';
import { cn } from '@/lib/utils';

interface SectionHeadingProps {
  tag?: string;
  title: string;
  subtitle?: string;
  accentWords?: string[]; // Слова, которые нужно выделить акцентным цветом
  className?: string;
  tagClassName?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  alignment?: 'left' | 'center' | 'right';
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const SectionHeading: React.FC<SectionHeadingProps> = ({
  tag,
  title,
  subtitle,
  accentWords = [],
  className,
  tagClassName,
  titleClassName,
  subtitleClassName,
  alignment = 'center',
  size = 'lg'
}) => {
  const alignmentClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  const sizeClasses = {
    sm: {
      tag: 'text-xs font-semibold',
      title: 'text-xl md:text-2xl font-bold',
      subtitle: 'text-sm'
    },
    md: {
      tag: 'text-sm font-semibold',
      title: 'text-2xl md:text-3xl font-bold',
      subtitle: 'text-base'
    },
    lg: {
      tag: 'text-sm font-semibold',
      title: 'text-3xl md:text-4xl font-bold',
      subtitle: 'text-lg'
    },
    xl: {
      tag: 'text-base font-semibold',
      title: 'text-4xl md:text-5xl font-bold',
      subtitle: 'text-xl'
    }
  };

  // Функция для выделения акцентных слов
  const highlightAccentWords = (text: string) => {
    if (accentWords.length === 0) return text;

    let highlightedText = text;
    accentWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'gi');
      highlightedText = highlightedText.replace(
        regex,
        `<span class="text-blue-600">${word}</span>`
      );
    });

    return highlightedText;
  };

  return (
    <div className={cn('mb-16', alignmentClasses[alignment], className)}>
      {tag && (
        <div className={cn(
          'inline-block px-3 py-1 mb-4 bg-blue-100 text-blue-600 rounded-full',
          sizeClasses[size].tag,
          tagClassName
        )}>
          {tag}
        </div>
      )}
      
      <h2 
        className={cn(
          'text-gray-900 mb-4',
          sizeClasses[size].title,
          titleClassName
        )}
        dangerouslySetInnerHTML={{ __html: highlightAccentWords(title) }}
      />
      
      {subtitle && (
        <p className={cn(
          'text-gray-600 max-w-3xl',
          alignment === 'center' && 'mx-auto',
          sizeClasses[size].subtitle,
          subtitleClassName
        )}>
          {subtitle}
        </p>
      )}
    </div>
  );
};

export default SectionHeading;
