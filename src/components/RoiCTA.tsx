'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useRoiStore } from '@/store/roiStore';
import Button from './Button';

const RoiCTA: React.FC = () => {
  const { showResults, setShowModal } = useRoiStore();

  if (!showResults) return null;

  return (
    <motion.div
      className="mt-12 text-center"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 1.6 }}
    >
      <div className="bg-white rounded-xl shadow-lg p-8">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
          Ready to Turn These Numbers Into Reality?
        </h2>
        <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
          Submit a request, and we&apos;ll prepare a personalized implementation plan for you.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            variant="primary"
            size="lg"
            onClick={() => setShowModal(true)}
            className="px-8"
          >
            Get Implementation Plan
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

export default RoiCTA;
