import React from 'react';
import { cn } from '@/lib/utils';
import FadeInSection from './FadeInSection';

interface TimelineStep {
  id: string;
  title: string;
  description: string;
  duration?: string;
  icon?: string | React.ReactNode;
  details?: string[];
}

interface TimelineProps {
  steps: TimelineStep[];
  className?: string;
  variant?: 'vertical' | 'horizontal';
  color?: 'blue' | 'green' | 'purple' | 'indigo';
  showDuration?: boolean;
  animated?: boolean;
}

const Timeline: React.FC<TimelineProps> = ({
  steps,
  className,
  variant = 'horizontal',
  color = 'blue',
  showDuration = true,
  animated = true
}) => {
  const colorClasses = {
    blue: {
      line: 'bg-blue-200',
      circle: 'bg-blue-600 text-white',
      accent: 'text-blue-600'
    },
    green: {
      line: 'bg-green-200',
      circle: 'bg-green-600 text-white',
      accent: 'text-green-600'
    },
    purple: {
      line: 'bg-purple-200',
      circle: 'bg-purple-600 text-white',
      accent: 'text-purple-600'
    },
    indigo: {
      line: 'bg-indigo-200',
      circle: 'bg-indigo-600 text-white',
      accent: 'text-indigo-600'
    }
  };

  if (variant === 'vertical') {
    return (
      <div className={cn('relative', className)}>
        {/* Вертикальная линия */}
        <div className={cn(
          'absolute left-8 top-0 bottom-0 w-0.5',
          colorClasses[color].line
        )} />
        
        <div className="space-y-12">
          {steps.map((step, index) => (
            <FadeInSection
              key={step.id}
              delay={animated ? index * 200 : 0}
              className="relative flex items-start"
            >
              {/* Иконка/номер */}
              <div className={cn(
                'relative z-10 flex items-center justify-center w-16 h-16 rounded-full text-xl font-bold',
                colorClasses[color].circle
              )}>
                {step.icon || (index + 1)}
              </div>
              
              {/* Контент */}
              <div className="ml-6 flex-1">
                <div className="flex items-center gap-4 mb-2">
                  <h3 className="text-xl font-semibold text-gray-900">
                    {step.title}
                  </h3>
                  {showDuration && step.duration && (
                    <span className={cn(
                      'px-3 py-1 text-sm font-medium rounded-full bg-gray-100',
                      colorClasses[color].accent
                    )}>
                      {step.duration}
                    </span>
                  )}
                </div>
                <p className="text-gray-600 mb-3">{step.description}</p>
                {step.details && (
                  <ul className="text-sm text-gray-500 space-y-1">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start">
                        <span className="text-gray-400 mr-2">•</span>
                        {detail}
                      </li>
                    ))}
                  </ul>
                )}
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>
    );
  }

  // Горизонтальная версия
  return (
    <div className={cn('relative', className)}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {steps.map((step, index) => (
          <FadeInSection
            key={step.id}
            delay={animated ? index * 200 : 0}
            className="text-center relative"
          >
            {/* Соединительная линия (кроме последнего элемента) */}
            {index < steps.length - 1 && (
              <div className={cn(
                'hidden lg:block absolute top-8 left-full w-full h-0.5 -translate-x-4',
                colorClasses[color].line
              )} />
            )}
            
            {/* Иконка/номер */}
            <div className={cn(
              'relative z-10 w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center text-xl font-bold',
              colorClasses[color].circle
            )}>
              {step.icon || (index + 1)}
            </div>
            
            {/* Заголовок и длительность */}
            <div className="mb-4">
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                {step.title}
              </h3>
              {showDuration && step.duration && (
                <span className={cn(
                  'inline-block px-3 py-1 text-sm font-medium rounded-full bg-gray-100',
                  colorClasses[color].accent
                )}>
                  {step.duration}
                </span>
              )}
            </div>
            
            {/* Описание */}
            <p className="text-gray-600 mb-3">{step.description}</p>
            
            {/* Детали */}
            {step.details && (
              <ul className="text-sm text-gray-500 space-y-1 text-left">
                {step.details.map((detail, detailIndex) => (
                  <li key={detailIndex} className="flex items-start">
                    <span className="text-gray-400 mr-2">•</span>
                    {detail}
                  </li>
                ))}
              </ul>
            )}
          </FadeInSection>
        ))}
      </div>
    </div>
  );
};

export default Timeline;
