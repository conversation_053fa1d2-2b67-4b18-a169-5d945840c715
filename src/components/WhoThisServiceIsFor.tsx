import React from 'react';
import FadeInSection from '@/components/FadeInSection';

interface PainPoint {
  id: string;
  headline: string;
  description: string;
  solution: string;
  icon: string;
  color: string;
}

const painPoints: PainPoint[] = [
  {
    id: 'leads-falling-through',
    headline: 'Leads Are Falling Through the Cracks',
    description: 'Your team manages deals in spreadsheets and chat apps. Some inquiries get lost or forgotten, and you\'re unsure if every opportunity is followed up.',
    solution: 'After Kommo implementation, every lead is automatically captured and tracked—no more missed revenue.',
    icon: '🕳️',
    color: 'from-red-500 to-red-600'
  },
  {
    id: 'crm-adoption-fails',
    headline: 'CRM Adoption Fails',
    description: 'You invested in a CRM, but your team resists using it. Data is incomplete, reports are unreliable, and the system feels like a burden.',
    solution: 'We audit your workflows, tailor <PERSON><PERSON><PERSON> to fit your processes, and train your team for real adoption and results.',
    icon: '❌',
    color: 'from-orange-500 to-orange-600'
  },
  {
    id: 'no-pipeline-visibility',
    headline: 'No Pipeline Transparency',
    description: 'You can\'t see where deals get stuck or how your sales team is performing. Forecasting is guesswork, and management lacks real-time visibility.',
    solution: 'With custom pipelines and automated reporting, you gain full control and insight into your sales process.',
    icon: '🔍',
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 'manual-processes',
    headline: 'Too Much Manual Work',
    description: 'Your team spends hours on data entry, follow-ups, and administrative tasks instead of selling. Productivity suffers and deals move slowly.',
    solution: 'Automated workflows handle routine tasks, freeing your team to focus on closing deals and building relationships.',
    icon: '⚙️',
    color: 'from-purple-500 to-purple-600'
  }
];

interface WhoThisServiceIsForProps {
  className?: string;
}

const WhoThisServiceIsFor: React.FC<WhoThisServiceIsForProps> = ({ className = '' }) => {
  return (
    <section className={`py-20 bg-gradient-to-br from-gray-50 to-blue-50 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <FadeInSection>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Who This Service Is For
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              If any of these scenarios sound familiar, Kommo CRM implementation can transform your business operations
            </p>
          </div>
        </FadeInSection>

        {/* Pain Points Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {painPoints.map((point, index) => (
            <FadeInSection key={point.id} delay={200 + index * 150}>
              <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-1 h-full">
                {/* Icon and Headline */}
                <div className="flex items-start mb-6">
                  <div className={`w-16 h-16 bg-gradient-to-br ${point.color} bg-opacity-10 rounded-2xl flex items-center justify-center text-3xl mr-4 flex-shrink-0`}>
                    {point.icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight">
                      {point.headline}
                    </h3>
                  </div>
                </div>

                {/* Problem Description */}
                <div className="mb-6">
                  <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg mb-4">
                    <p className="text-gray-700 leading-relaxed">
                      <span className="font-semibold text-red-700">The Problem:</span> {point.description}
                    </p>
                  </div>
                </div>

                {/* Solution */}
                <div className="bg-green-50 border-l-4 border-green-400 p-4 rounded-r-lg">
                  <p className="text-gray-700 leading-relaxed">
                    <span className="font-semibold text-green-700">Our Solution:</span> {point.solution}
                  </p>
                </div>
              </div>
            </FadeInSection>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhoThisServiceIsFor;
