'use client';

import { create } from 'zustand';
import { RoiInput, RoiResult, calculateRoi } from '@/utils/calculateRoi';

interface RoiStore {
  // Form data
  formData: RoiInput;
  setFormData: (data: Partial<RoiInput>) => void;
  
  // Results
  results: RoiResult | null;
  setResults: (results: RoiResult | null) => void;
  
  // UI state
  showResults: boolean;
  setShowResults: (show: boolean) => void;

  showModal: boolean;
  setShowModal: (show: boolean) => void;
  
  // Actions
  calculateResults: () => void;
  resetCalculator: () => void;

  // Navigation
  scrollToResults: () => void;
  scrollToForm: () => void;
}

const initialFormData: RoiInput = {
  leads: 0,
  conversion: 0,
  avgDealSize: 0,
  timePerLead: 0,
  niche: '',
  managers: 0,
  channels: [],
};

export const useRoiStore = create<RoiStore>((set, get) => ({
  // Initial state
  formData: initialFormData,
  results: null,
  showResults: false,
  showModal: false,
  
  // Setters
  setFormData: (data) => set((state) => ({
    formData: { ...state.formData, ...data }
  })),
  
  setResults: (results) => set({ results }),
  
  setShowResults: (show) => set({ showResults: show }),
  
  setShowModal: (show) => set({ showModal: show }),
  
  // Actions
  calculateResults: () => {
    const { formData } = get();

    // Validate required fields
    if (formData.leads <= 0 || formData.conversion <= 0 || formData.avgDealSize <= 0) {
      return;
    }

    const results = calculateRoi(formData);
    set({
      results,
      showResults: true
    });

    // Update URL with anchor and scroll to results
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.history.pushState(null, '', '#results');
      }
      get().scrollToResults();
    }, 100);
  },
  
  resetCalculator: () => {
    set({
      formData: initialFormData,
      results: null,
      showResults: false,
      showModal: false
    });

    // Remove anchor from URL and scroll back to form
    setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.history.pushState(null, '', window.location.pathname);
      }
      get().scrollToForm();
    }, 100);
  },

  // Navigation helpers
  scrollToResults: () => {
    const resultsElement = document.getElementById('roi-results');
    if (resultsElement) {
      resultsElement.scrollIntoView({ behavior: 'smooth' });
    }
  },

  scrollToForm: () => {
    const formElement = document.getElementById('roi-form');
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' });
    }
  },
}));
